package com.xyy.saas.transmitter.server.dal.mysql.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.task.TransmissionTaskRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据传输-记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionTaskRecordMapper extends BaseMapperX<TransmissionTaskRecordDO> {

    /**
     * 构建查询条件
     */
    default LambdaQueryWrapperX<TransmissionTaskRecordDO> buildQueryWrapper(TransmissionTaskRecordPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TransmissionTaskRecordDO>()
            .eqIfPresent(TransmissionTaskRecordDO::getTenantId, reqVO.getTenantId())
            .inIfPresent(TransmissionTaskRecordDO::getTenantId, reqVO.getTenantIds())
            .eqIfPresent(TransmissionTaskRecordDO::getId, reqVO.getId())
            .inIfPresent(TransmissionTaskRecordDO::getId, reqVO.getIds())
            .eqIfPresent(TransmissionTaskRecordDO::getOrganId, reqVO.getOrganId())
            .eqIfPresent(TransmissionTaskRecordDO::getUpstreamTaskId, reqVO.getUpstreamTaskId())
            .eqIfPresent(TransmissionTaskRecordDO::getBusinessNo, reqVO.getBusinessNo())
            .eqIfPresent(TransmissionTaskRecordDO::getServicePackId, reqVO.getServicePackId())
            .eqIfPresent(TransmissionTaskRecordDO::getConfigItemId, reqVO.getConfigItemId())
            .eqIfPresent(TransmissionTaskRecordDO::getOrganType, reqVO.getOrganType())
            .eqIfPresent(TransmissionTaskRecordDO::getNodeType, reqVO.getNodeType())
            .eqIfPresent(TransmissionTaskRecordDO::getApiCode, reqVO.getApiCode())
            .eqIfPresent(TransmissionTaskRecordDO::getOriginalParams, reqVO.getOriginalParams())
            .eqIfPresent(TransmissionTaskRecordDO::getRequestParams, reqVO.getRequestParams())
            .eqIfPresent(TransmissionTaskRecordDO::getResponseResult, reqVO.getResponseResult())
            .eqIfPresent(TransmissionTaskRecordDO::getRequestStatus, reqVO.getRequestStatus())
            .inIfPresent(TransmissionTaskRecordDO::getRequestStatus, reqVO.getRequestStatusList())
            .eqIfPresent(TransmissionTaskRecordDO::getAllowRetry, reqVO.getAllowRetry())
            .eqIfPresent(TransmissionTaskRecordDO::getRetryCount, reqVO.getRetryCount())
            .eqIfPresent(TransmissionTaskRecordDO::getMaxRetryCount, reqVO.getMaxRetryCount())
            .eqIfPresent(TransmissionTaskRecordDO::getErrorMessage, reqVO.getErrorMessage())
            .betweenIfPresent(TransmissionTaskRecordDO::getExpectedTime, reqVO.getExpectedTime())
            .betweenIfPresent(TransmissionTaskRecordDO::getActualTime, reqVO.getActualTime())
            .betweenIfPresent(TransmissionTaskRecordDO::getCompleteTime, reqVO.getCompleteTime())
            .eqIfPresent(TransmissionTaskRecordDO::getPriority, reqVO.getPriority())
            .betweenIfPresent(TransmissionTaskRecordDO::getCreateTime, reqVO.getCreateTime())
            .likeIfPresent(TransmissionTaskRecordDO::getFullName, reqVO.getFullName())
            .eqIfPresent(TransmissionTaskRecordDO::getIdCard, reqVO.getIdCard())
            .orderByDesc(TransmissionTaskRecordDO::getId);
    }

    default PageResult<TransmissionTaskRecordDO> selectPage(TransmissionTaskRecordPageReqVO reqVO) {
        return selectPage(reqVO, buildQueryWrapper(reqVO));
    }

    default List<TransmissionTaskRecordDO> selectList(TransmissionTaskRecordPageReqVO reqVO) {
        return selectList(buildQueryWrapper(reqVO));
    }

}