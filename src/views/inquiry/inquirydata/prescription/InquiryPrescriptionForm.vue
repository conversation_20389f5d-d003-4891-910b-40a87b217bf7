<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%">
    <el-form ref="formRef" :rules="rules" v-loading="formLoading" :model="pricingForm">

      <ContentWrap title="条形码" >
        <el-row class="barcodeContianer">
              <geteBarCode v-if="barcodeData.length" :barcodeData="barcodeData"/>
        </el-row>
      </ContentWrap>

      <ContentWrap title="处方信息">
        <el-row>
          <el-col :span="6" class="pd-row">处方编号: {{ formData.pref }}</el-col>
          <!-- 远程审方 -->
          <el-col :span="6" class="pd-row" v-if="formData.inquiryBizType == InquiryBizTypeEnum.REMOTE.value">三方处方单: {{ formData.thirdPrescriptionNo }}</el-col>
          <el-col :span="6" class="pd-row">医生姓名: {{ formData.doctorName }}</el-col>
          <el-col :span="6" class="pd-row">审核药师: {{ formData.pharmacistName }}</el-col>
          <el-col :span="6" class="pd-row">处方状态: <dict-tag :type="DICT_TYPE.PRESCRIPTION_STATUS" :value="formData.status!" /></el-col>
          <el-col :span="6" class="pd-row" v-if="formData.status == 5">驳回原因: {{ formData.invalidReason }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6" class="pd-row">临床诊断: {{ formData.diagnosisName }}</el-col>
          <el-col :span="6" class="pd-row">用药类型: <dict-tag :type="DICT_TYPE.PRESCRIPTION_MEDICINE_TYPE" :value="formData.medicineType!" /></el-col>
          <el-col :span="6" class="pd-row" v-if="formType == 'detail'">客户端渠道: <dict-tag :type="DICT_TYPE.CLIENT_CHANNEL_TYPE" :value="formData.clientChannelType" /></el-col>
          <el-col :span="6" class="pd-row">
            开方时间:
            <el-date-picker v-if="formType == 'update'" v-model="formData.outPrescriptionTime" type="datetime"
              :placeholder="formData.outPrescriptionTime" />
            <span v-else>{{ formatDate(formData.outPrescriptionTime) }}</span>
          </el-col>
          <el-col :span="6" class="pd-row">药师审方时间: {{ formatDate(formData.auditPrescriptionTime) }}</el-col>  
          <el-col :span="6" class="pd-row" v-if="formData.medicineType">
             副数（中药）:
             {{
                [
                 formData.ext?.tcmTotalDosage && `共${formData.ext.tcmTotalDosage}副`,
                 formData.ext?.tcmDailyDosage && `每日${formData.ext.tcmDailyDosage}副`,
                 formData.ext?.tcmUsage && `每日服用${formData.ext.tcmUsage}次`
                ]
                  .filter(Boolean)
                 .join(',')
             }}
          </el-col>
          <el-col :span="6" class="pd-row" v-if="formData.medicineType">用法（中药）:{{ formData.ext?.tcmDirections}}</el-col>
          <el-col :span="6" class="pd-row" v-if="formData.medicineType">加工方式（中药）: {{ formData.ext?.tcmProcessingMethod}}</el-col>
  
          <!-- 远程审方 -->
          <!--
          <template v-if="formData.inquiryBizType == InquiryBizTypeEnum.REMOTE.value">
            <el-col :span="6" class="pd-row">
              问诊记录: 
              <template v-if="formData.inquiryWayType == 1 && formData.imPdf">
                <el-button size="small" type="primary" @click="showPDF()">查看记录</el-button>
                <PdfOrVideoDialog ref="refPdfOrVideoDialog" type="pdf" :src="formData.imPdf" />
              </template>
              <template v-else-if="formData.inquiryWayType == 2 && formData.mp4Url">
                <el-button size="small" type="primary" @click="showPDF()">查看记录</el-button>
                <PdfOrVideoDialog ref="refPdfOrVideoDialog" type="video" :src="formData.mp4Url" />
              </template>
              <template v-else>&nbsp;</template>
            </el-col>
          </template>
          -->

          <el-col :span="6" class="pd-row" v-if="formData.ext?.pricingStatus == 1 && formType == 'pricing'">
            <el-form-item label="结算发票号:" prop="setlInvoiceNumber">
              <el-input v-model="pricingForm.setlInvoiceNumber" maxlength="32" placeholder="请输入结算发票号"/>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="pd-row" v-else-if="pricingForm.setlInvoiceNumber">结算发票号: {{ pricingForm.setlInvoiceNumber }}</el-col>
          <el-col :span="6" class="pd-row" v-if="formType == 'pricing' && (formData.ext?.pricingStatus == 0 || formData.ext?.pricingStatus == null)">
            <el-form-item label="处方金额:" prop="pricingPrice">
              <el-input-number v-model="pricingForm.pricingPrice" :min="0" :max="9999.99" :precision="2" :step="0.1" placeholder="请输入大于0小于9999.99的正数,小数位不可超过两位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="pd-row">
          <el-form-item label="扩展信息:" prop="desc" v-if="formType == 'update'" >
            <el-input v-model="formData.desc" type="textarea" />
          </el-form-item>
        </el-row>
      </ContentWrap>

    

      <ContentWrap title="患者信息">
        <el-row class="pd-row">
          <el-col :span="6">姓名: {{ formData.patientName }}</el-col>
          <el-col :span="6">性别: <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="formData.patientSex!" /></el-col>
          <el-col :span="6">手机号: {{ formData.patientMobile }}</el-col>
          <el-col :span="6">年龄: {{ formData.patientAge }}</el-col>
        </el-row>
        <el-row class="pd-row">
          <el-col :span="6">身份证号: {{ formData.patientIdCard }}</el-col>
          <el-col :span="6">过敏史: {{ formData.allergic }}</el-col>
          <el-col :span="6">肝、肾功能异常: {{ getIntDictOptions(DICT_TYPE.LIVER_KIDNEY_VALUE).find((item) => item.value === formData.liverKidneyValue)?.label }}</el-col>
          <el-col :span="6" v-if="formData.patientSex == 2">是否妊娠、哺乳期: {{ getIntDictOptions(DICT_TYPE.GESTATION_LACTATION_VALUE).find((item) => item.value === formData.gestationLactationValue)?.label }}</el-col>
        </el-row>
      </ContentWrap>

<!-- 如果是编辑 -->
      <ContentWrap title="商品信息" v-if="formType=='update'">
        <el-table :data="details" header-align="center">
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column label="药品名称" prop="commonName" min-width="100" />
          <el-table-column label="规格" prop="attributeSpecification" >
            <template #default="scope">
              <el-input v-model="scope.row.attributeSpecification" />
            </template>
          </el-table-column>
          <el-table-column label="用法" prop="directions" >
            <template #default="scope">
              <el-select
                  v-model="scope.row.directions"
                  placeholder="请选择用法"
                  clearable
                  size="large"
                >
                  <el-option
                    v-for="dict in selectDict.directions"
                    :key="dict.id"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
            </template>
          </el-table-column>
          <el-table-column label="用量" prop="singleDose" >
            <template #default="scope">
              <el-input v-model="scope.row.singleDose" />
            </template>
          </el-table-column>
          <el-table-column label="剂量单位" prop="singleUnit" >
            <template #default="scope">
               <el-select
                  v-model="scope.row.singleUnit"
                  placeholder="请选择剂量单位"
                  clearable
                  size="large"
                >
                  <el-option
                    v-for="dict in selectDict.unit"
                    :key="dict.id"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
            </template>
          </el-table-column>
          <el-table-column label="频次" prop="useFrequency" >
            <template #default="scope">
              <el-select
                  v-model="scope.row.useFrequency"
                  placeholder="请选择频次"
                  clearable
                  size="large"
                >
                  <el-option
                    v-for="dict in selectDict.frequency"
                    :key="dict.id"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="quantity" >
            <template #default="scope">
              <el-input v-model="scope.row.quantity" />
            </template>
          </el-table-column>

          <el-table-column
            v-if="formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.NEED_PRICING && formType == 'pricing'"
            label="单价" prop="productPrice" min-width="140" align="center">
            <template #default="{ row }">
              <el-input-number v-model="row.productPrice" min="0" max="9999" :precision="2" :step="0.1"
                @change="changeProductPrice(row)" />
            </template>
          </el-table-column>

          <el-table-column v-if="(formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.NEED_PRICING || formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.PRICED_DETAIL)
            && formType !== 'pricing'" label="单价" prop="productPrice" />

          <el-table-column
            v-if="formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.NEED_PRICING || formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.PRICED_DETAIL"
            label="合计" prop="actualAmount" />
        </el-table>

      <el-row :gutter="22" style="margin-top: 10px" v-if="pricingForm.pricingPrice" >
        <el-col :span="20"/>
        <el-col :span="4" style="font-size: medium;font-weight: bold" >总金额 {{pricingForm.pricingPrice}} 元</el-col>
      </el-row>
    </ContentWrap>
<!-- 如果是划价/详细 -->
    <ContentWrap title="商品信息" v-else>
        <el-table :data="details" header-align="center">
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column label="药品名称" prop="commonName" min-width="100" />
          <el-table-column label="规格" prop="attributeSpecification" />
          <el-table-column label="用法" prop="directions" />
          <el-table-column label="用量" prop="singleDose" />
          <el-table-column label="剂量单位" prop="singleUnit" />
          <el-table-column label="频次" prop="useFrequency" />
          <el-table-column label="数量" prop="quantity" />

          <el-table-column
            v-if="formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.NEED_PRICING && formType == 'pricing'"
            label="单价/元" prop="productPrice" min-width="140" align="center">
            <template #default="{ row }" >
              <el-form :model="row" :rules="rules" ref="priceForm" >
                <el-form-item prop="productPrice" class="isproductPrice" >
                  <el-input-number v-model="row.productPrice" :min="0" :max="9999.99" :precision="2" :step="0.1"  placeholder="请输入单价"  @change="changeProductPrice(row)" />
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>

          <el-table-column v-if="(formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.NEED_PRICING || formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.PRICED_DETAIL)
            && formType !== 'pricing'" label="单价/元" prop="productPrice" />

          <el-table-column
            v-if="formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.NEED_PRICING || formData.ext?.pricingStatus == PrescriptionPricingStatusEnum.PRICED_DETAIL"
            label="合计/元" prop="actualAmount" />
        </el-table>

      <el-row :gutter="22" style="margin-top: 10px" v-if="pricingForm.pricingPrice" >
        <el-col :span="20"/>
        <el-col :span="4" style="font-size: medium;font-weight: bold" >总金额 {{pricingForm.pricingPrice}} 元</el-col>
      </el-row>
    </ContentWrap> 
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button v-if="formType == 'pricing' && formData.ext?.pricingStatus == 1 " @click="submitPricing(1)"   :disabled="formLoading">暂存</el-button>
      <el-button v-if="formType == 'pricing'||formType == 'update'" @click="submitPricing(2)" type="primary" :disabled="formLoading">确认</el-button>

    </template>
  </Dialog>
</template>
<script setup lang="ts">
import geteBarCode from './components/geteBarCode.vue'
import { InquiryPrescriptionApi, InquiryPrescriptionVO } from '@/api/inquiry/inquirydata/prescription'
import {DICT_TYPE, getIntDictOptions,getDictOptions} from "@/utils/dict";
import { formatDate} from "@/utils/formatTime";
import {
  InquiryPrescriptionDetailApi,
  InquiryPrescriptionDetailVO
} from "@/api/inquiry/inquirydata/prescription/detailIndex";
import * as TenantApi from "@/api/system/tenant";
import {findNode} from "@/utils/tree";
import { PrescriptionPricingStatusEnum, InquiryBizTypeEnum } from "@/utils/constants";
import PdfOrVideoDialog from "../PdfOrVideoDialog.vue";

import * as DictDataApi from '@/api/system/dict/dict.data'
  
/** 处方记录 表单 */
defineOptions({ name: 'InquiryPrescriptionForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({})
const details = ref<InquiryPrescriptionDetailVO[]>([]) // 处方详情
const formRef = ref() // 表单 Ref
 const barcodeData=ref([])
// 编辑字典列表
const selectDict=ref({
  frequency:[],
  directions:[],
  unit:[]
})

const pricingForm = ref({
  prescriptionPref:"",
  setlInvoiceNumber:"",
  pricingPrice:0,
  type:null, // 1-暂存 2-确认
  detailPricingVos:[]
})
const checkPrice=(row,value,callback) => {
  if(Number(pricingForm.value.pricingPrice) <= 0 || pricingForm.value.pricingPrice == undefined){
    callback(new Error('处方金额必须大于0'))
  }
  callback()
}

const checkPrice2=(row,value,callback) => {
  if(Number(pricingForm.value.pricingPrice) <= 0 || pricingForm.value.pricingPrice == undefined){
    message.warning("金额请输入不为0的正数")
    callback(new Error(''))
  }
  callback()
}
const rules = ref({
  setlInvoiceNumber: [{ required: true, message: '请输入结算发票号', trigger: 'blur' }],
  pricingPrice: [{ required: true, validator:checkPrice, trigger: 'blur' }],
  productPrice: [{ required: true, validator:checkPrice2, trigger: 'blur' }],
})


/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InquiryPrescriptionApi.getInquiryPrescription(id)
      pricingForm.value.setlInvoiceNumber = formData.value.ext?.setlInvoiceNumber
      pricingForm.value.pricingPrice = formData.value.ext?.pricingPrice
      details.value = await InquiryPrescriptionDetailApi.getInquiryPrescriptionDetail(formData.value.pref);

        barcodeData.value = [
        {
          title: '处方编号',
          codeType: 'pref',
          code: formData.value.pref
        },
        {
          title: '医院机构编码',
          codeType: 'institutionCode',
          code: formData.value.institutionCode
        },
        {
          title: '医师编码',
          codeType: 'hospitalPref',
          code: formData.value.doctorPref
        }
      ]
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InquiryPrescriptionVO
    if (formType.value === 'create') {
      await InquiryPrescriptionApi.createInquiryPrescription(data)
      message.success(t('common.createSuccess'))
    } else {
      await InquiryPrescriptionApi.updateInquiryPrescription(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const changeProductPrice = (row) =>{
  row.actualAmount = row.quantity * row.productPrice;
  pricingForm.value.pricingPrice = details.value.reduce((prev, curr)=>  prev + curr.quantity * curr.productPrice,0).toFixed(2)
}
//单价/元红框
const priceForm=ref()
const submitPricing = async(type:number)=>{
  // 校验表单
  if (!formRef) return
  Promise.all([formRef?.value?.validate(),priceForm?.value?.validate()]).then(async () => {
      // 提交请求
      formLoading.value = true
      try {
      // 划价操作
        if (formType.value === 'pricing') {
          pricingForm.value.prescriptionPref = formData.value.pref;
          pricingForm.value.type = type;
          pricingForm.value.detailPricingVos = []
          for (let val of details.value) {
            if(val.productPrice == null && type == 2){ // 非暂存
              message.warning("请填写商品["+val.commonName+"]的单价")
              return;
            }
            let p = {
              id:val.id,
              productPrice:val.productPrice
            }
            if(formData.value.ext?.pricingStatus == 1){ // 划明细
              pricingForm.value.detailPricingVos.push(p);
            }
          }
          if(type == 2){
            message.confirm('您当前选择的处方编号为'+pricingForm.value.prescriptionPref
              +',请确认提交当前处方笺的结算金额为'+pricingForm.value.pricingPrice+'元，提交后将无法修改', '处方单结算', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              await InquiryPrescriptionApi.pricingInquiryPrescription(pricingForm.value);
              message.success(t('common.updateSuccess'))
              dialogVisible.value = false
              // 发送操作成功的事件
              emit('success')
            }).catch(() => {
            });
          }else{
            try{
              await InquiryPrescriptionApi.pricingInquiryPrescription(pricingForm.value);
              message.success(t('common.updateSuccess'))
              dialogVisible.value = false
              // 发送操作成功的事件
              emit('success')
            }catch{

            }
          }
        }
      //编辑操作
        if(formType.value === 'update'){
          // 提交请求
          console.log(details.value[0]);
          console.log(formData.value);
          
          const data={...formData.value}
          console.log('data',data);
          try{
            await InquiryPrescriptionApi.updateInquiryPrescription(data)
            message.success(t('common.updateSuccess'))
            dialogVisible.value = false
            // 发送操作成功的事件
            emit('success')
          }finally {
            formLoading.value = false
          }
        }
      } finally {
        formLoading.value = false
      }
  }).catch (() => {
      console.log('失败');   
  })
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
  pricingForm.value = {
    prescriptionPref:"",
    setlInvoiceNumber:"",
    pricingPrice:0,
    type:null, // 1-暂存 2-确认
    detailPricingVos:[]
  }
  barcodeData.value=[]
}

// 问诊记录
const refPdfOrVideoDialog = ref<any>(null)
const showPDF = () => {
  refPdfOrVideoDialog.value?.open()
}

onMounted(async ()=>{
  selectDict.value.frequency=await DictDataApi.getDictAllData({type:"drug_use_frequency"})
  selectDict.value.directions=await DictDataApi.getDictAllData({type:"drug_directions"})
  selectDict.value.unit=await DictDataApi.getDictAllData({type:"drug_dose_unit"})
})
</script>

<style lang="scss" scoped>
.pd-row{
  margin-top: 10px;
}
:deep(.cell){
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
}
.isproductPrice{
  position: absolute;
  top: 50%;
  right: 35px;
  transform: translateY(-50%);
  color: red;
  font-size: 12px;
}
.error{
  border: 1px solid red;
  border-radius: 5px;
}
.barcodeContianer{}
</style>
