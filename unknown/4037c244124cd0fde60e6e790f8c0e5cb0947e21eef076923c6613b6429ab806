package com.xyy.saas.inquiry.enums.transmitter;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 重试策略枚举
 */
@Getter
@AllArgsConstructor
public enum RetryStrategyEnum {

    FIXED(1, "固定间隔", "每次重试使用相同的时间间隔"),
    INCREMENTAL(2, "递增间隔", "每次重试的间隔时间递增"),
    EXPONENTIAL(3, "指数退避", "重试间隔呈指数增长");

    /**
     * 策略编码
     */
    private final Integer code;

    /**
     * 策略名称
     */
    private final String name;

    /**
     * 策略描述
     */
    private final String description;

    /**
     * 获取策略名称
     *
     * @param code 策略编码
     * @return 策略名称
     */
    public static String getName(Integer code) {
        if (code == null) {
            return "";
        }
        for (RetryStrategyEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return "";
    }
} 