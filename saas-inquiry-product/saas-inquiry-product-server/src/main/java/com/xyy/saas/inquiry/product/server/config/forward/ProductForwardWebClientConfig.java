package com.xyy.saas.inquiry.product.server.config.forward;

import com.xyy.saas.inquiry.config.webclient.ForwardWebClientConfig;
import com.xyy.saas.inquiry.config.webclient.InquiryForwardProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 转发旧问诊服务配置
 *
 * @Author: cxy
 */
@Slf4j
@Configuration
public class ProductForwardWebClientConfig {

    @Bean
    public InquiryProductForwardClient inquiryProductForwardClient(@Qualifier("forwardWebClient") WebClient webClient, InquiryForwardProperties inquiryForwardProperties) {
        return ForwardWebClientConfig.getHttpServiceClient(webClient, inquiryForwardProperties, InquiryProductForwardClient.class);
    }


    @Bean
    public InquiryDubboForwardClient inquiryDubboForwardClient(@Qualifier("dubboForwardWebClient") WebClient webClient, InquiryForwardProperties inquiryForwardProperties) {
        return ForwardWebClientConfig.getHttpServiceClient(webClient, inquiryForwardProperties.getDubbo(), InquiryDubboForwardClient.class);
    }
}
