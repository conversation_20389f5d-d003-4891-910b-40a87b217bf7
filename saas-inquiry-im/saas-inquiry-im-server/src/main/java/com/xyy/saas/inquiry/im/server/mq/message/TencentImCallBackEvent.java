package com.xyy.saas.inquiry.im.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: xucao
 * @Date: 2024/11/29 13:35
 * @Description: 腾讯IM回调事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class TencentImCallBackEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "TENCENT_IM_CALL_BACK";

    private String msg;


    @JsonCreator
    public TencentImCallBackEvent(@JsonProperty("msg") String msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
