package com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 医院科室信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_hospital_department_relation")
@KeySequence("saas_inquiry_hospital_department_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryHospitalDepartmentRelationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 医院编码
     */
    private String hospitalPref;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 科室id
     */
    private Long deptId;
    /**
     * 科室编码,eg:101
     */
    private String deptPref;
    /**
     * 科室名称,eg:内科
     */
    private String deptName;

    /**
     * 父级科室id
     */
    private Long deptParentId;

    /**
     * 状态（0正常 1停用）
     */
    private Integer disabled;
}