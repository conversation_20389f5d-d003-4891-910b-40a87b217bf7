package com.xyy.saas.inquiry.drugstore.api.option.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum;
import com.xyy.saas.inquiry.pojo.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 问诊配置选项 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryOptionConfigDto extends BaseDto {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 目标类型 {@link InquiryTargetTypeEnum}
     */
    private Integer targetType;
    /**
     * 选项类型 {@link InquiryOptionTypeEnum}
     */
    private Integer optionType;
    /**
     * 区域编码或者租户id
     */
    private Long targetId;
    /**
     * 区域名称或者租户名称
     */
    private String targetName;
    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 配置选项类型
     */
    private String optionName;
    /**
     * 配置值
     */
    private String optionValue;
    /**
     * 配置描述
     */
    private String description;

    /**
     * 是否使用此配置 0开启，1关闭 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer used;


    /**
     * 处方拓展字段
     */
    private String ext;
}