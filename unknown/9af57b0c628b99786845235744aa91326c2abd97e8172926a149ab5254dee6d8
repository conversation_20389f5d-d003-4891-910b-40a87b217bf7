package com.xyy.saas.inquiry.signature.server.dal.mysql.person;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonPageReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 签章平台用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquirySignaturePersonMapper extends BaseMapperX<InquirySignaturePersonDO> {

    default PageResult<InquirySignaturePersonDO> selectPage(InquirySignaturePersonPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquirySignaturePersonDO>()
            .eqIfPresent(InquirySignaturePersonDO::getUserId, reqVO.getUserId())
            .eqIfPresent(InquirySignaturePersonDO::getOpenUserId, reqVO.getOpenUserId())
            .likeIfPresent(InquirySignaturePersonDO::getAccountName, reqVO.getAccountName())
            .likeIfPresent(InquirySignaturePersonDO::getUserName, reqVO.getUserName())
            .eqIfPresent(InquirySignaturePersonDO::getMobile, reqVO.getMobile())
            .eqIfPresent(InquirySignaturePersonDO::getBankAccountNo, reqVO.getBankAccountNo())
            .eqIfPresent(InquirySignaturePersonDO::getUserIdentType, reqVO.getUserIdentType())
            .eqIfPresent(InquirySignaturePersonDO::getUserIdentNo, reqVO.getUserIdentNo())
            .eqIfPresent(InquirySignaturePersonDO::getSealId, reqVO.getSealId())
            .eqIfPresent(InquirySignaturePersonDO::getUserStatus, reqVO.getUserStatus())
            .betweenIfPresent(InquirySignaturePersonDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquirySignaturePersonDO::getId));
    }


    default InquirySignaturePersonDO selectOneByCondition(InquirySignaturePersonPageReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<InquirySignaturePersonDO>()
            .eqIfPresent(InquirySignaturePersonDO::getUserId, reqVO.getUserId())
            .eqIfPresent(InquirySignaturePersonDO::getOpenUserId, reqVO.getOpenUserId())
            .eqIfPresent(InquirySignaturePersonDO::getSignaturePlatform, reqVO.getSignaturePlatform())
            .eqIfPresent(InquirySignaturePersonDO::getSignaturePlatformConfigId, reqVO.getSignaturePlatformConfigId()), false
        );
    }

}