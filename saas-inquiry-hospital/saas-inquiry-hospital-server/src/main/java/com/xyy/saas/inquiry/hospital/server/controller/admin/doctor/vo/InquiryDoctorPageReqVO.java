package com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 医生信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryDoctorPageReqVO extends PageParam {

    @Schema(description = "医生pref", example = "1276")
    private String pref;

    @Schema(description = "医生名称", example = "芋艿")
    private String name;

    @Schema(description = "性别 1男 2女")
    private Integer sex;

    @Schema(description = "身份证号码")
    private String idCard;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "用户ID", example = "8822")
    private Long userId;

    @Schema(description = "审核状态 0、待审核  1、审核通过  2、审核驳回", example = "2")
    private Integer auditStatus;

    @Schema(description = "在线状态 0、离线  1、在线", example = "1")
    private Integer onlineStatus;

    @Schema(description = "医院编码", example = "1")
    private String hospitalPref;

    @Schema(description = "科室id", example = "1")
    private Long deptId;

    @Schema(description = "合作状态：0未合作 1 合作中 2禁用合作 3过期")
    private Integer cooperation;

    @Schema(description = "最后接诊时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastInquiryTime;

    @Schema(description = "开始接诊时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startInquiryTime;

    @Schema(description = "结束停诊时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endInquiryTime;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

    @Schema(description = "证件照地址")
    private String photo;

    @Schema(description = "个人简介")
    private String biography;

    @Schema(description = "擅长专业,eg:擅长神经内科诊疗")
    private String professionalDec;

    @Schema(description = "医生类型： 1全职医生 2兼职医生", example = "1")
    private Integer jobType;

    @Schema(description = "是否开启密码,0:不开启,1:开启", example = "2")
    private Boolean prescriptionPasswordStatus;

    @Schema(description = "开方密码")
    private String prescriptionPassword;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "医生id集合", example = "1")
    private List<Long> doctorIds;

    @Schema(description = "医生pref集合", example = "1")
    private List<String> doctorPrefs;

    @Schema(description = "签章平台", example = "1")
    private Integer signaturePlatform;

    @Schema(description = "实名认证状态 0: 待认证，1: 认证完成，2: 认证失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer certifyStatus;

    @AssertTrue(message = "签章平台不能为空")
    @JsonIgnore
    public boolean isSignaturePlatformValid() {
        return certifyStatus == null || signaturePlatform != null;
    }

}