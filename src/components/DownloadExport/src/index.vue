<!-- 下载导出组件 -->
<template>
  <el-button @click="handleExport" class="button-primary"> 下载 </el-button>
  <!-- 下载弹窗 -->
  <el-dialog v-model="columnFilterVisible" title="提示" width="50%">
    <div>
      <span>请输入下载Excel名称</span>
      <el-input v-model="inputValue" style="width: 340px; margin-left: 12px" placeholder="" />
    </div>
    <template #footer>
      <el-button @click="columnFilterVisible = false">取消</el-button>
      <el-button type="primary" @click="handleExportConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="tsx">
import { ProductInfoApi } from '@/api/inquiry/product/product'
import download from '@/utils/download'
defineOptions({ name: 'DownloadExport' })

const props = defineProps({
  //start  以下参数是等后端统一了导出接口后再做修改，需要支持选中列导出和筛选条件导出
  // selectItem: {
  //   // 选中的列的值
  //   type: Array,
  //   default: () => {
  //     return []
  //   }
  // },
  // parameFilter: {
  //   // 搜索框参数
  //   type: Object,
  //   default: () => {
  //     return {}
  //   }
  // },
  // title: {
  //   // 标题
  //   type: String,
  //   default: ''
  // },
  // exportType: {
  //   // 导出类型
  //   type: String,
  //   default: ''
  // }
  //end 结束

  //目前支持情况
  title: {
    // 标题
    type: String,
    default: ''
  },
  codeArr: {
    // 导出字段名
    type: Array,
    default: () => {
      return []
    }
  }
})

const inputValue = ref('')
const columnFilterVisible = ref(false)
const handleExport = () => {
  columnFilterVisible.value = true
}
const handleExportConfirm = async () => {
  // if (props.selectItem.length > 0) {
  //   // 此时说明有勾选中的数据，以勾选中的数据为主
  // } else {
  //   // 否则，以搜索框参数为主
  // }
  const data = await ProductInfoApi.exportProductInfo(props.codeArr)
  download.excel(data, inputValue.value)
  columnFilterVisible.value = false
}
onMounted(() => {
  let today = new Date()
  let year = today.getFullYear()
  let month = (today.getMonth() + 1).toString().padStart(2, '0')
  let date = today.getDate().toString().padStart(2, '0')
  inputValue.value = `${props.title}${year}-${month}-${date}`
})
</script>
<style scoped>
.button-primary {
  padding: 17px 16px;
  border-radius: 4px;
  background-color: #00b955;
  color: #ffffff;
  margin: 0 12px;
}
</style>
