package cn.iocoder.yudao.module.system.api.dict;

import cn.iocoder.yudao.module.system.api.dict.dto.DictTypeRespDTO;
import cn.iocoder.yudao.module.system.convert.dict.DictConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO;
import cn.iocoder.yudao.module.system.service.dict.DictTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 字典数据 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class DictTypeApiImpl implements DictTypeApi {

    @Resource
    private DictTypeService dictTypeService;

    @Override
    public DictTypeRespDTO getDictType(String type) {
        DictTypeDO dictType = dictTypeService.getDictType(type);
        return DictConvert.INSTANCE.convert(dictType);
    }
}
