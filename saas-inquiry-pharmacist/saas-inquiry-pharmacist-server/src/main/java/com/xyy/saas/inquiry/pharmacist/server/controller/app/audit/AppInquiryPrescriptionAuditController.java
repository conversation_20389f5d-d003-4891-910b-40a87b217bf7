package com.xyy.saas.inquiry.pharmacist.server.controller.app.audit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.concurrent.TimeUnit;


@Tag(name = "APP+PC - 处方审核核心接口")
@RestController
@RequestMapping(value = {"/admin-api/kernel/pharmacist/prescription-audit", "/app-api/kernel/pharmacist/prescription-audit"})
@Validated
public class AppInquiryPrescriptionAuditController {

    @Resource
    private InquiryPrescriptionAuditService prescriptionAuditService;


    @GetMapping("/wait-receive-count")
    @Operation(summary = "获取当前药师待审核处方数量")
    @Idempotent(timeout = 300, timeUnit = TimeUnit.MILLISECONDS, keyResolver = UserIdempotentKeyResolver.class) // user维度锁住 默认300ms
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Long> waitReceiveCount() {
        return prescriptionAuditService.waitReceiveCount();
    }

    @GetMapping("/receive-prescription")
    @Operation(summary = "领取一个待审核的处方")
    @Idempotent(keyResolver = UserIdempotentKeyResolver.class) // user维度锁住 默认1s
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<InquiryPrescriptionReceiveVO> receivePrescription() {
        return prescriptionAuditService.receivePrescription();
    }

    @GetMapping("/audit-timeout")
    @Operation(summary = "获取处方超时时间-单位秒")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Integer> getAuditTimeout() {
        return prescriptionAuditService.getAuditTimeout();
    }


    @PostMapping("/audit-pass")
    @Operation(summary = "审核通过")
    @Idempotent // 参数维度锁住 默认1s
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> auditPass(@RequestBody @Valid InquiryPrescriptionAuditVO auditVO) {
        auditVO.setAuditStatus(PrescriptionAuditStatusEnum.APPROVED);
        return prescriptionAuditService.auditPass(auditVO);
    }

    @PostMapping("/audit-reject")
    @Operation(summary = "审核驳回")
    @Idempotent // 参数维度锁住 默认1s
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<Boolean> auditReject(@RequestBody @Valid InquiryPrescriptionAuditVO auditVO) {
        auditVO.setAuditStatus(PrescriptionAuditStatusEnum.REJECTED);
        return prescriptionAuditService.auditReject(auditVO);
    }

    // @PostMapping("/create")
    // @Operation(summary = "创建处方审核记录")
    // @PreAuthorize("@ss.hasPermission('pharmacist:prescription-audit:create')")
    // public CommonResult<Long> createPrescriptionAudit(@Valid @RequestBody InquiryPrescriptionAuditSaveReqVO createReqVO) {
    //     return success(prescriptionAuditService.createPrescriptionAudit(createReqVO));
    // }
    //
    // @PutMapping("/update")
    // @Operation(summary = "更新处方审核记录")
    // @PreAuthorize("@ss.hasPermission('pharmacist:prescription-audit:update')")
    // public CommonResult<Boolean> updatePrescriptionAudit(@Valid @RequestBody InquiryPrescriptionAuditSaveReqVO updateReqVO) {
    //     prescriptionAuditService.updatePrescriptionAudit(updateReqVO);
    //     return success(true);
    // }
    //
    // @DeleteMapping("/delete")
    // @Operation(summary = "删除处方审核记录")
    // @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('pharmacist:prescription-audit:delete')")
    // public CommonResult<Boolean> deletePrescriptionAudit(@RequestParam("id") Long id) {
    //     prescriptionAuditService.deletePrescriptionAudit(id);
    //     return success(true);
    // }

    @GetMapping("/get")
    @Operation(summary = "获得处方审核记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<InquiryPrescriptionAuditRespVO> getPrescriptionAudit(@RequestParam("id") Long id) {
        InquiryPrescriptionAuditDO prescriptionAudit = prescriptionAuditService.getPrescriptionAudit(id);
        return success(BeanUtils.toBean(prescriptionAudit, InquiryPrescriptionAuditRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得处方审核记录分页")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<PageResult<InquiryPrescriptionAuditRespVO>> getPrescriptionAuditPage(@Valid InquiryPrescriptionAuditPageReqVO pageReqVO) {
        PageResult<InquiryPrescriptionAuditDO> pageResult = prescriptionAuditService.getPrescriptionAuditPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryPrescriptionAuditRespVO.class));
    }


}