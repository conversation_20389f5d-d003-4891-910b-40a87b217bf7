package com.xyy.saas.inquiry.product.enums;

import java.util.Objects;

/**
 * desc 商品删除类型：
 *    1. 仅删除商品信息
 *    2. 删除全部记录
 *    3. 首营审批
 *    4. 商品拆零
 *    9. 编辑商品
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductDeleteTypeEnum {

    // 仅删除商品信息
    ONLY_PRODUCT(1, "仅删除商品信息"),
    // 删除全部记录
    ALL_RECORD(2, "删除全部记录"),

    ;

    public final int code;
    public final String desc;

    ProductDeleteTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductDeleteTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductDeleteTypeEnum type : values()) {
            if (Objects.equals(type.code, code)) {
                return type;
            }
        }
        return null;
    }
}
