package com.xyy.saas.inquiry.im.server.service.tencent;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.Map;

/**
 * @ClassName：TencentImClient
 * @Author: xucao
 * @Date: 2024/11/28 10:09
 * @Description: 调腾讯Im接口
 */
@HttpExchange(accept = "application/json", contentType = "application/json")
public interface TencentImClient {

    @PostExchange("/{apiPath}")
    Map<String,Object> callApi(@PathVariable("apiPath") String apiPath, @RequestBody Map<String, Object> params);
}
