<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <!-- 搜索表单 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="商品信息" prop="mixedQuery">
        <el-input
          v-model="queryParams.mixedQuery"
          placeholder="请输入商品编码/通用名称/品牌名称/条形码/助记码/批准文号/生产厂家"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="商品分类" prop="businessScope">
        <el-select
          v-model="queryParams.businessScope"
          placeholder="请选择商品分类"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_PRODUCT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否拆零" prop="businessScope">
        <el-select
          v-model="queryParams.businessScope"
          placeholder="请选择是否拆零"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_BUSINESS_SCOPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery" class="bc-00b955">查询</el-button>
        <el-button @click="resetQuery" >重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 商品列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      @selection-change="handleSelectionChange"
    >
<!--      <template #default="scope">-->
<!--        <div class="flex items-center">-->
<!--          <el-image-->
<!--            v-if="scope.row.imageUrl"-->
<!--            :src="scope.row.imageUrl"-->
<!--            class="w-60px h-60px mr-10px"-->
<!--          />-->
<!--          <div v-else class="w-60px h-60px mr-10px flex-center bg-gray-100">-->
<!--            <Icon icon="ep:picture" class="text-20px" />-->
<!--          </div>-->
<!--          <div class="flex-1">-->
<!--            <div>品牌名称: {{ scope.row.brandName }}</div>-->
<!--            <div>通用名称: {{ scope.row.commonName }}</div>-->
<!--            <div>规格/型号: {{ scope.row.spec }}</div>-->
<!--            <div>单位: {{ scope.row.unit }}</div>-->
<!--            <div>条形码: {{ scope.row.barcode }}</div>-->
<!--            <div>批准文号: {{ scope.row.approvalNumber }}</div>-->
<!--            <div>生产厂家: {{ scope.row.manufacturer }}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </template>-->

      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column label="商品信息" min-width="300">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-image :src="row.images?.[0] || ''" class="w-20 h-20 mr-2" fit="contain" />
            <div>
              <div>【{{ row.brandName }}】{{ row.commonName }} {{ row.spec }}</div>
              <div class="text-gray-500">批准文号：{{ row.approvalNumber }}</div>
              <div class="text-gray-500" v-if="!!row.showPref">商品编码：{{ row.showPref }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="条形码" prop="barcode" />
      <el-table-column label="规格/型号" prop="spec" />
      <el-table-column label="生产厂家" prop="manufacturer" />
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <template #footer >
      <el-button @click="dialogVisible = false" class="mt10">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" class="mt10"> 确 定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ProductInfoApi } from '@/api/inquiry/product/product'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict' 
const message = useMessage()
const dialogVisible = ref(false)
const dialogTitle = ref('') // 弹窗的标题
const loading = ref(false)
const list = ref([])
const total = ref(0)
const selectedProducts = ref([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  mixedQuery: ''
})

/** 打开弹窗 */
const open = (info) => {
  dialogTitle.value = info
  dialogVisible.value = true
  getList()
}

defineExpose({ open })

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await ProductInfoApi.getProductInfoPage(queryParams)
    console.log(res);
    
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.mixedQuery = ''
  handleQuery()
}

/** 选择商品操作 */
const handleSelectionChange = (selection) => {
  selectedProducts.value = selection
}

/** 确定按钮操作 */
const emit = defineEmits(['select'])
const handleSubmit = () => {
  if (selectedProducts.value.length === 0) {
    return message.error('请选择商品')
  }
  emit('select', selectedProducts.value)
  dialogVisible.value = false
}
</script>
