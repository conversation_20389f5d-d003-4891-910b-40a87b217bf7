<template>
	<view class="content" style="display: flex">
		<web-view v-if='baseUrl' allow="camera; microphone" ref="webview" :src="baseUrl" @message="getAPPMessage"
			@onPostMessage="getAPPMessage" @load="webViewLoad" @error="webViewError" style="flex: 1" />

	</view>
</template>

<script>
	import sheep from '@/sheep';
	import bridge from "./utils/bridge"
	import $store from '@/sheep/store';
	import permision from "@/js_sdk/wa-permission/permission.js"
	import AppHandle from "@/class/AppHandle"
	import NoticeBox from "@/components/NoticeBox/index.vue"
	const appHandle = new AppHandle()
	// #ifdef APP-PLUS
	import MobilePush from "@/class/MobilePush.js"
	const mobilePush = new MobilePush()
	// #endif
	export default {
		data() {
			return {

				// #ifdef APP-PLUS  
				message1: '我是 app 平台',
				// #endif    
				// #ifdef MP-WEIXIN    
				message2: '我是小程序平台',
				// #endif   
				// #ifdef H5
				message3: '我是小程序平台',
				// #endif   	
				baseUrl: ``,
				mobilePush: null,
				webView: null,
				title: 'Hello',
				currentWebview: {},
				bradge: {
					closeApp: () => {
						this.back();
					},
					returnLogin: () => {
						uni.clearStorageSync();
						uni.redirectTo({
							url: '/pages/index/login',
						});
					},
					setNavBarTitle: (value) => {
						uni.setNavigationBarTitle({
							title: value
						});
					}
				},
				commonPageList: ["/privacy", "/consultation"],
				// 安装包下载进度
				downloadProgress: 0,
				downloadTimer: null,
				isOutTime: 0,
				isOutTimer: null,
				mobilePushTimer: null,
				mobilePushCount: 0,
			};
		},
		components: {
			NoticeBox
		},
		onReady() {

		},
		beforeDestroy() {
			clearInterval(this.mobilePushTimer)
			this.mobilePushTimer = null
			this.mobilePushCount = 0
		},
		onLoad(option) {
			let isReviewPackage = 1
			let appletData = "";
			console.info("========option", option)
			if (option.appletData) {
				appletData = option.appletData
				console.info("========appletData", appletData)
			}
			console.info(this.$refs)
			const deviceType = this.getDeviceType()
			let deviceInfo = uni.getDeviceInfo()
			console.log("deviceInfo", JSON.stringify(deviceInfo))
			deviceInfo = `${deviceInfo.deviceModel||deviceInfo.model}`

			let redirect = ""
			if (option.redirect) {
				redirect = option.redirect
			}
			let baseUrl = `${sheep.$store('app').webViewUrl}`
			console.log("baseUrl 0 ==>", baseUrl)

			const pages = getCurrentPages();
			const page = pages[pages.length - 1];
			if (page.options && page.options.params) {
				baseUrl += page.options.params
			} else if (page.$page && page.$page.options && page.$page.options.params) {
				baseUrl += page.$page.options.params
			}
			console.log("baseUrl 1 ==>", baseUrl)

			const {
				accessToken = '', multiTenant = false, userId = ''
			} = uni.getStorageSync('loginUser');
			const appStore = $store("app")
			console.log("appStore.info.versionCode", appStore.info.versionCode)
			console.log("appStore.info.version", appStore.info.version)
			let pushData = ""
			if (option.pushData) {
				pushData = option.pushData
			}

			baseUrl +=
				`?accessToken=${accessToken}&multiTenant=${Number(!!multiTenant)}&userId=${userId}&deviceType=${deviceType}&deviceInfo=${deviceInfo}&redirect=${redirect}&appletData=${appletData}&version=${appStore.info.version}&versionCode=${appStore.info.versionCode}&isReviewPackage=${isReviewPackage}&pushData=${pushData}`
			console.log("baseUrl 2 ==>", baseUrl)
			
			// 添加随机参数_t防止缓存
			baseUrl = this.urlSetTimestamp(baseUrl)
			console.log("baseUrl 3 ==>", baseUrl)

			this.baseUrl = baseUrl

			// #ifdef APP-PLUS
			// app单独配置头部
			this.currentWebview = page.$getAppWebview();
			console.log("this.currentWebview", JSON.stringify(this.currentWebview))
			const chatStore = $store('chat');
			setTimeout(() => {
				this.webView = this.currentWebview.children()[0]
				console.log("this.webView", this.webView)
				console.log("JSON.stringify(this.webView)", JSON.stringify(this.webView))
				chatStore.webView = this.currentWebview.children()[0]
			}, 1000)
			this.currentWebview.setStyle({
				// titleNView: {
				// 	backgroundColor: '#FFFFFF', // 设置背景颜色
				// 	borderColor: '#DDDDDD', // 设置边框颜色
				// 	autoBackButton: true, // 自动显示返回按钮
				// },	
				titleNView: false,
				scalable: false,
			});
			// #endif
			// #ifdef H5
			// 端收消息
			window.addEventListener("message", (e) => {
				console.log('UNIAPP接收到H5消息', JSON.stringify(e))
				if (!this.webView && e) {
					this.webView = e.source
				}
				if (e.data && e.data.data) {
					const resultData = e.data.data.arg
					bridge.apply(this, [resultData])
					console.log('UNIAPP接收到H5消息', JSON.stringify(resultData))
				}
			}, false)
			// #endif

			// 接收公共消息
			uni.$on('pushMessage', (data) => {
				console.info("uni的webview接收新消息", data)
				this.sendRequestData({ // 向H5发送消息
					result: data,
					originData: {
						type: 'sendUniData'
					}
				}, 2)
				if (data.eventType === 'sendInquiry') { // 收到医生工作台更新消息
					console.info("收到医生工作台更新消息")
				}
				if (data.eventType === 'issuePrescription') { // 收到医生工作台更新消息
					console.info("医生开具处方")
				}
				if (data.eventType === 'cancelPrescription') { // 收到医生工作台更新消息
					console.info("医生取消处方")
				}
				if (data.eventType === 'issuePrescriptionTimeout') { // 收到医生工作台更新消息
					console.info("医生处方超时")
				}
				if (data.eventType === 'issuePrescriptionEvaluate') { // 收到医生工作台更新消息
					console.info("医生问诊评价")
				}
				if (data.eventType === 'prescriptionAudit') { // 收到医生工作台更新消息
					console.info("处方已被审核")
				}
			})
			// #ifdef APP-PLUS
			// 接收到移动推送消息
			uni.$on('mobilePushMessage', (data) => {
				uni.removeStorageSync("mobilePushData")
				console.info("uni接收到移动推送消息", data)
				this.sendRequestData({ // 向H5发送消息
					result: data,
					originData: {
						type: 'sendUniData'
					}
				}, 2)
			})
			if (appStore.mobilePush) {
				appStore.mobilePush.bindAccount()
			}
			this.checkMobilePush()
			// #endif
		},

		onHide() {
			uni.setStorageSync('webviewState', 0)
			// #ifdef APP-PLUS
			this.sendRequestData({ // 向H5发送消息
				result: {
					type: 'appStatus',
					appStatus: "onHide"
				},
				originData: {
					type: 'sendUniData'
				}
			}, 2)
			// #endif
		},
		onUnload() {

		},
		async onShow() {
			// const webviewState = uni.getStorageSync('webviewState')
			// console.log("webviewState", webviewState)
			// uni.setStorageSync('webviewState', 1)
			console.info("onShow")
			// 推送绑定账号
			// #ifdef APP-PLUS
			this.sendRequestData({ // 向H5发送消息
				result: {
					type: 'appStatus',
					appStatus: "onShow"
				},
				originData: {
					type: 'sendUniData'
				}
			}, 2)
			const appStore = $store('app');
			if (appStore.mobilePush) {
				appStore.mobilePush.bindAccount()
			}
			// #endif
			console.log("=============app show=============")
			// uni.showLoading({
			// 	title:'加载中',
			// 	icon:"none"
			// })
			// setTimeout(()=>{
			// 		uni.hideLoading()
			// },100)
			// this.checkTimConnect()
			// #ifdef APP-PLUS

			this.sendRequestData({
				result: 'onShow',
				originData: {
					type: 'sendUniData'
				}
			}, 2)
			// 判断当前是否是公用页面，如果公用直接返回，不需要判断是否登录
			const pages = getCurrentPages();
			console.log(this.commonPageList, pages.options.parmas)
			if (this.commonPageList.includes(pages.options.parmas)) {
				return
			}

			// #endif

		},
		onBackPress(options) {
			// console.log("返回事件触发：", options);
			const androidHandle = () => {
				var main = plus.android.runtimeMainActivity();
				plus.android.invoke(main, "moveTaskToBack", false);
			}
			const iosHandle = () => {
				if (this.isOutTime < 3) {
					if (this.isOutTime == 0) {
						plus.nativeUI.toast("再按一次退出应用");
					} else {
						plus.runtime.quit();
					}
					if (this.isOutTimer) {

						this.isOutTime = 0
						clearInterval(this.isOutTimer)
						this.isOutTimer = null
					}
					this.isOutTimer = setInterval(() => {
						this.isOutTime++
						console.info("this.isOutTime", this.isOutTime)
						if (this.isOutTime > 2) {

							this.isOutTime = 0
							clearInterval(this.isOutTimer)
							this.isOutTimer = null
						}
					}, 1000)
				} else {
					clearInterval(this.isOutTimer)
					this.isOutTimer = null
					this.isOutTime = 0
				}
			}
			if (options.from === 'navigateBack') {
				
				// 来源是uni.navigateBack，不阻止默认行为
				return false;
			} else {
				// 获取当前打开过的页面路由数组
				let routes = getCurrentPages();
				// 后台进入
				if(routes.length===0){
					console.log("后台进入")
				}
				// 执行自定义的返回处理逻辑
				let webviewRouter = uni.getStorageSync('webviewRouter')
				console.info("===========webviewRouter::===========", JSON.stringify(webviewRouter))
				// 是否默认走返回
				const isgoback = uni.getStorageSync('isgoback')
				if (isgoback && isgoback == 1) {
					navigateBack()
				}
				if (webviewRouter) {
					webviewRouter = JSON.parse(webviewRouter)


					// 视频问诊页面阻止返回
					console.info("webviewRouter::", JSON.stringify(webviewRouter))
					if (webviewRouter.toPath.includes('/inquiry/waiting') && webviewRouter.from.includes(
							'/Inquiry/form')) {
						uni.showToast({
							icon: "none",
							title: "系统正在为您派单，请耐心等待!"
						})
						return true;
					}
					// 医生小助理页面阻止返回
					if (webviewRouter.toPath.includes('/inquiry/chatBefore/index') && webviewRouter.from.includes(
							'/Inquiry/form')) {
						// uni.showToast({
						// 	icon: "none",
						// 	title: "系统正在为您派单，请耐心等待!"
						// })
						this.sendRequestData({ // 向H5发送消息
							result: "ChatBefore",
							originData: {
								type: 'sendUniData'
							}
						})
						return true;
					}
					if (webviewRouter.toPath.includes('/doctorWork') && webviewRouter.from.includes(
							'doctorWork')) {
						this.sendRequestData({ // 向H5发送消息
							result: "BackHome",
							originData: {
								type: 'sendUniData'
							}
						})
						return true;
					}
					// 阻止患者处方页返回到消息列表页面
					let isChatChildH5 = uni.getStorageSync('isChatChildH5')
					if (isChatChildH5) {
						isChatChildH5 = 1
					}
					console.log("isChatChildH5", isChatChildH5)
					// 抢单 - 推送进来
					if(webviewRouter.from.includes("mobilePush=1")||webviewRouter.toPath.includes("mobilePush=1")){
						this.sendRequestData({ // 向H5发送消息
							result: "BackHome",
							originData: {
								type: 'sendUniData'
							}
						})
								return true;
					}
					if (
						isChatChildH5 == 1 || (webviewRouter.toPath.includes('/patientDrugApplyInfo?inquiryPref=') &&
							webviewRouter.from ===
							'/') ||
						(webviewRouter.toPath.includes('/doctorCheck?inquiryPref=') && webviewRouter.from === '/') ||
						(webviewRouter.toPath.includes('/online-medical-cases?inquiryPref=') && webviewRouter.from === '/')

					) {
						console.info("patientDrugApplyInfo:::webviewRouter.toPath", webviewRouter.toPath)
						console.info("patientDrugApplyInfo:::webviewRouter.from", webviewRouter.from)
						this.sendRequestData({ // 向H5发送消息
							result: "BackChatPage",
							originData: {
								type: 'sendUniData'
							}
						})
						return true;
					}
					// 首页返回到桌面
					if (webviewRouter.toPath.slice(0, 5) === ('/home') || webviewRouter.toPath === '/notify' ||
						webviewRouter
						.toPath === '/user') {
						const deviceType = this.getDeviceType()
						if (deviceType == 'Android') {
							androidHandle()
						}
						if (deviceType == 'IOS') {
							iosHandle()
						}
					} else {
						navigateBack()
					}
				}
				console.info("自定义逻辑", webviewRouter)
				// 退出当前应用，该方法只在App中生效
				// Android 使用 Native.js 调用原生接口
				// var main = plus.android.runtimeMainActivity();
				// plus.android.invoke(main, "moveTaskToBack", false);
				// 阻止默认的返回行为
				return true;
			}
			// uni.showModal({
			// 	title: '退出该页面后将关闭此次轨迹记录',
			// 	confirmText: '退出',
			// 	success: function(res) {
			// 		if (res.confirm) {
			// 			// .....//此处把你退出后需要的方法写上就行
			// 			this.stopGetLocation();
			// 			this.started = false;
			// 			this.imgpath = '../../static/open.png'
			// 		}
			// 	}.bind(this)//此处bind是关键
			// })
		},
		methods: {
			toTest() {
				uni.redirectTo({
					url: "/pages/inquiry/drugInfoConfirm?inquiryPref=" + 111624 +
						'&isFromVideo=1',
				})
				// let url =
				// 	`/pages/inquiry/chat/index?name=${'test'}&avatar=${''}&inquiryPref=111624&source=/notify&accessToken=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
				// uni.navigateTo({
				// 	url
				// });
			},
			sendRequestData(res, plt) { //发送
				let param = JSON.stringify({
					type: res.originData.type,
					data: res
				})
				// #ifdef H5
				this.webView.postMessage({
					type: res.originData.type,
					data: res
				})
				// #endif
				// #ifdef APP-PLUS
				this.webView.evalJS(`requestData(${param})`);
				// #endif

				// #ifdef MP-WEIXIN
				// 	let baseUrl=this.baseUrl.replace(/&requestData=[^&]*/g, ''); 

				// 	let mpparam = JSON.stringify({
				// 			type: res.originData.type,
				// 			data: res
				// 		})
				// 		this.baseUrl=''
				// 	this.baseUrl =
				// 		`${baseUrl}&requestData=${encodeURIComponent(JSON.stringify(mpparam))}`
				// 		console.info("baseUrl",	this.baseUrl)
				// #endif
				// if (plt == 1) { //h5
				// 	this.webView.postMessage({
				// 		type: res.originData.type,
				// 		data: res
				// 	})
				// } else { //app
				// 	this.webView.evalJS(`requestData(${param})`);
				// }
			},
			checkTimConnect() {
				console.info("===开始TIM登陆检测===")
				let isLogin = 1;
				if (!chatStore.imObj || !chatStore.imObj.chat || !chatStore.imObj.chat?.isReadyFlag) {
					isLogin = 0;
					chatStore.startConnect()
				}
				console.info("===TIM登陆检测结果===", isLogin ? '已登录' : '未登录,重新登陆发起')
			},
			initTim() {
				// 是否IM登录发起
				const chatStore = $store('chat');
				const userStore = $store('user');
				console.info("userStore.isLogin", userStore.isLogin)
				const imIsLogin = uni.getStorageSync('imIsLogin');
				console.info("imIsLogin:", imIsLogin)
				// let isReady = chat.isReady();
				// console.log("+++chatStore+++", chatStore.imObj.chat)
				if (!imIsLogin || Number(imIsLogin) === 0 || !chatStore.imObj || !chatStore.imObj.chat || !chatStore.imObj
					.chat?.isReadyFlag || !chatStore.imObj.chat) {
					chatStore.startConnect()
				}

			},
			choosePhoto() {
				const _this = this;
				uni.chooseImage({
					count: 3, //最多可以选择的图片张数，默认9
					// sourceType: ['album'], //album 从相册选图，camera 使用相机，默认二者都有。如需直接开相机或直接选相册，请只使用一个选项
					sizeType: ['original'], //original 原图，compressed 压缩图，默认二者都有
					success(res) {
						console.log('选择图片完成', res)
						// 调用上传图片的接口
						_this.uploadPhoto(res.tempFilePaths);
					},
					fail() {
						console.log('失败', err);
					},
					complete() {
						console.log('结束');
					},
				})
			},

			//  获取当前设备运行类型
			getDeviceType() {
				let deviceType = ""
				//APP
				// #ifdef APP-PLUS
				const port = uni.getSystemInfoSync().platform
				if (port === 'android') {
					deviceType = 'Android'
				}
				if (port === 'ios') {
					deviceType = 'IOS'
				}
				// #endif
				//h5
				// #ifdef H5
				deviceType = 'H5';
				// #endif

				//小程序
				// #ifdef MP-WEIXIN
				deviceType = 'MP-WEIXIN';
				// #endif
				return deviceType
			},
			webViewLoad() {
				this.sendMessage();
				setTimeout(() => {
					const mobilePushData = uni.getStorageSync('mobilePushData')
					// 后台运行时的的通知
					if (mobilePushData) {
						console.log("mobilePushData", mobilePushData)
						uni.removeStorageSync("mobilePushData")
						this.sendRequestData({ // 向H5发送消息
							result: mobilePushData,
							originData: {
								type: 'sendUniData'
							}
						}, 2)
					}
				}, 1000)

			},
			// 检测是否存在通信
			checkMobilePush() {
				// #ifdef APP-PLUS
				if (this.mobilePushTimer) {
					clearInterval(this.mobilePushTimer)
					this.mobilePushTimer = null
					this.mobilePushCount = 0
				}
				this.mobilePushTimer = setInterval(() => {
					console.log("检测中++++++++++")
					this.mobilePushCount++
					const mobilePushData = uni.getStorageSync('mobilePushData')
					// 后台运行时的的通知
					if (this.mobilePushCount >= 10) {
						clearInterval(this.mobilePushTimer)
						this.mobilePushTimer = null
						this.mobilePushCount = 0
					}
					if (mobilePushData) {
						console.log("mobilePushData", mobilePushData)
						uni.removeStorageSync("mobilePushData")
						clearInterval(this.mobilePushTimer)
						this.mobilePushTimer = null
						this.mobilePushCount = 0
						this.sendRequestData({ // 向H5发送消息
							result: mobilePushData,
							originData: {
								type: 'sendUniData'
							}
						}, 2)
					}
				}, 1000)

				// #endif
			},
			webViewError() {
				console.log('webViewError')
				this.baseUrl = this.urlSetTimestamp(this.baseUrl)
				console.log("baseUrl 4 ==>", this.baseUrl)
			},
			sendMessage() {
				const {
					accessToken = '', multiTenant = false
				} = uni.getStorageSync('loginUser');
				//小程序和app 发送消息
				// #ifdef MP-WEIXIN
				//微信
				// console.log('发送消息', accessToken, multiTenant)
				// #endif
				//app
				// #ifdef APP-PLUS
				// #endif
			},

			getAPPMessage(e) {
				console.info("=============weixin接收==============", JSON.stringify(e))
				//小程序和app 接受消息
				let data = [];

				// #ifdef MP-WEIXIN
				console.info("=============weixin接收==============", JSON.stringify(e))
				data = e.target.data; //多次postMessage的参数数组
				if (e && e.detail && e.detail.data) {
					const resultData = e.detail.data[0]
					bridge.apply(this, [resultData])
					console.log('---getAPP收到消息', JSON.stringify(resultData))
				}
				// #endif

				// #ifdef APP-PLUS
				data = e.detail.data;

				//app端收消息
				if (e && e.detail && e.detail.data) {
					const resultData = e.detail.data[0]
					bridge.apply(this, [resultData])
					console.log('---getAPP收到消息', JSON.stringify(resultData))
					if (resultData.download) {
						const url = resultData.url
						console.log("下载app")
						this.downloadApp(url)
					}
					// 扫码功能
					if (resultData.type === 'scanCode') {
						this.scanCode()
					}
				}
				// #endif

				if (data[data.length - 1].name == 'closeApp') {
					this.bradge.closeApp();
				}
				if (data[data.length - 1].name == 'returnLogin') {
					this.bradge.returnLogin();
				}
			},
			back() {
				uni.showModal({
					title: '提示',
					content: '是否退出系统？',
					success: function(res) {
						if (res.confirm) {
							// 退出当前应用，该方法只在App中生效
							plus.runtime.quit();
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					},
				});
			},
			// 下载文件
			downloadApp(url) {
				// 如果是iso设备直接打开对应页面
				const deviceType = this.getDeviceType()
				if (deviceType === "IOS") {
					plus.runtime.openURL(url)
					return
				}

				this.downloadTimer = setInterval(() => {

					console.log("下载中")
					const data = {
						data: {
							result: this.downloadProgress
						}
					}
					this.webView.evalJS(`requestData(${JSON.stringify(data)})`);
					if (this.downloadProgress === 100) {
						clearInterval(this.downloadTimer)
						return
					}
				}, 300)
				const downloadTask = uni.downloadFile({
					url: url, //仅为示例，并非真实的资源
					success: (res) => {
						if (res.statusCode === 200) {
							console.log('下载成功');
							uni.hideLoading();
							const filePath = res.tempFilePath
							plus.runtime.install(filePath, {
								success: () => {
									console.log("安装成功");
									uni.showToast({
										title: "安装成功",
										icon: "success"
									});
								},
								fail: (err) => {
									console.error("安装失败：", err.message);
									uni.showToast({
										title: "安装失败",
										icon: "none"
									});
								}
							});
						}
					}
				});
				downloadTask.onProgressUpdate((res) => {
					console.log('下载进度' + res.progress);
					console.log('已经下载的数据长度' + res.totalBytesWritten);
					console.log('预期需要下载的数据总长度' + res.totalBytesExpectedToWrite);
					this.downloadProgress = res.progress
					// 在通知栏显示下载进度
				});
			},
			async requestPerimission() {
				// 判断系统类型
				const deviceType = this.getDeviceType()
				// IOS设备暂时没有做权限处理
				if (deviceType === "IOS") {
					const permisionIDIOS = 'camera'
					const resultIOS = await permision.judgeIosPermission(permisionIDIOS)
					if (!resultIOS) {
						uni.showToast({
							title: "请开启摄像头权限",
							icon: "none"
						})
						// 未授权则主动跳转到权限设置页面
						permision.gotoAppPermissionSetting()
					}
					return
				}
				// 安卓端设备判断当前是否有摄像头权限
				const permisionID = 'android.permission.CAMERA'
				var result = await permision.requestAndroidPermission(permisionID)
				if (result == 1) {
					console.log("已授权")
				} else {
					uni.showToast({
						title: "请开启摄像头权限",
						icon: "none"
					})
					// 未授权则主动跳转到权限设置页面
					permision.gotoAppPermissionSetting()
				}
			},
			async beforeScanCode() {
				return new Promise(async (resolve) => {
					var platform = uni.getSystemInfoSync().platform;
					if (platform == 'android') {
						// 相机
						const cameraPermission = await appHandle.nativeCheckPermission('Camera')
						console.log("cameraPermission", cameraPermission)
						if (cameraPermission != "granted") {
							plus.nativeUI.confirm(
								"荷叶问诊APP 需要授予相机权限，用于扫码加药", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					} else {
						return resolve(1)
					}
				})
			},
			//扫描条形码
			async scanCode() {
				const beforeScanCodeRes = await this.beforeScanCode()
				console.log("beforeScanCodeRes", beforeScanCodeRes)
				if (beforeScanCodeRes != 1) {
					return
				}
				// 判断当前是否有权限
				const toRequestAuthRes = await appHandle.toRequestAuth('camera')
				console.log("toRequestAuthRes", toRequestAuthRes)
				if (!toRequestAuthRes) {
					return
				}
				// 开启扫码
				uni.scanCode({
					onlyFromCamera: true,
					scanType: ['barCode'], // 当前只支持条形码
					success: (res) => {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
						// 将扫码结果返回给APP
						this.searchCode(res.result)
					},
					fail: (err) => {
						console.log('扫码失败。；');
					}
				})
			},
			// 将扫码结果返回给APP
			searchCode(code) {
				const data = {
					data: {
						result: code
					}
				}
				this.webView.evalJS(`requestData(${JSON.stringify(data)})`);
			},

			// url中添加_t参数
			urlSetTimestamp(url) {
				const timestamp = Date.now();
				const tsPattern = /([?&]_t=)([^&#]*)/g;

				// 检查是否存在 _t 参数
				if (tsPattern.test(url)) {
					// 替换现有 _t 参数值
					return url.replace(tsPattern, (match, prefix, oldValue) => prefix + timestamp);
				}

				// 无 _t 参数时追加
				const separator = url.includes('?') ? '&' : '?';
				const hashIndex = url.indexOf('#');

				if (hashIndex !== -1) {
					// 在哈希片段前插入参数
					return url.substring(0, hashIndex) + separator + '_t=' + timestamp + url.substring(hashIndex);
				}

				// 无哈希片段时直接追加
				return url + separator + '_t=' + timestamp;
			}
		},
	};
</script>

<style scoped>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
</style>