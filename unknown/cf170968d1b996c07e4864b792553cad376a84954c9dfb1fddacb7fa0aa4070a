package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.v5_1.client.TemplateClient;
import com.fasc.open.api.v5_1.req.template.SignTemplateDetailReq;
import com.fasc.open.api.v5_1.res.template.SignTemplateDetailRes;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法大大 合同模板相关
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/23 18:36
 */
@Component
@Slf4j
public class FddTemplateService extends FddBaseService {

    private static final Map<Integer, TemplateClient> templateClientMap = new HashMap<>();

    private TemplateClient getTemplateClient(Integer configId) {
        if (templateClientMap.get(configId) == null) {
            synchronized (FddTemplateService.class) {
                if (templateClientMap.get(configId) == null) {
                    templateClientMap.put(configId, new TemplateClient(getOpenApiClient(configId)));
                }
            }
        }
        return templateClientMap.get(configId);
    }

    /**
     * 查询签署任务模板详情
     */
    public CommonResult<SignTemplateDetailRes> signTemplateDetail(FddBaseReqDto<SignTemplateDetailReq> fddBaseReqDto) {
        return execute((t) -> getTemplateClient(fddBaseReqDto.getConfigId()).getSignTemplateDetail(t), fddBaseReqDto, "查询签署任务模板详情");
    }


}
