import { service } from './service'

import { config } from './config'

const { default_headers, request_timeout } = config

const request = (option: any) => {
  const { url, method, params, data, headersType, responseType, timeout, ignoreError, isLoop, ...config } = option
  return service({
    url: url,
    method,
    params,
    data,
    ...config,
    timeout: timeout || request_timeout,
    responseType: responseType,
    headers: {
      'Content-Type': headersType || default_headers,
      'clientChannelType':1
    },
    ignoreError,
    isLoop: isLoop || false,
  })
}
export default {
  get: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  getOriginal: async (option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res // 返回完整响应
  },
  post: async <T = any>(option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res.data as unknown as T
  },
  postOriginal: async (option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res
  },
  delete: async <T = any>(option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  put: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  putOriginal: async (option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res // 返回完整响应
  },
  download: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  downloadPost: async <T = any>(option: any) => {
    const res = await request({ method: 'POST', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  }
}
