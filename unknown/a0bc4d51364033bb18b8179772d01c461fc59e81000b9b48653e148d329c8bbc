package com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo;

import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.signature.SignatureContractExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 签章合同新增/修改 Request VO")
@Data
@Builder
@Accessors(chain = true)
public class InquirySignatureContractStatusVO {

    @Schema(description = "合同号")
    @NotNull(message = "合同号不能为空")
    private String pref;

    @Schema(description = "签章平台  1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer signaturePlatform;

    /**
     * {@link com.xyy.saas.inquiry.enums.signature.ContractTypeEnum}
     */
    @Schema(description = "合同业务类型")
    private Integer contractType;

    @Schema(description = "业务方唯一标识")
    private String bizId;

    @Schema(description = "三方签署任务id signTaskId")
    private String thirdId;

    @Schema(description = "合同状态")
    private Integer contractStatus;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "自绘合同标识")
    private Integer selfDrawn;

    @Schema(description = "参与方集合")
    private List<ParticipantItem> participants;
    /**
     * 合同图片
     */
    @Schema(description = "合同图片url")
    private String imgUrl;
    /**
     * 合同PDF文件
     */
    @Schema(description = "合同PDF文件url")
    private String pdfUrl;

    @Schema(description = "同步平台")
    private Integer syncPlatform;

    @Schema(description = "同步平台状态")
    private Integer syncPlatformStatus;

    @Schema(description = "签章合同扩展信息ext")
    private SignatureContractExtDto ext;


}