package com.xyy.saas.inquiry.signature.server.api.signature;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureImageApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureImageDto;
import com.xyy.saas.inquiry.signature.server.util.ImageUtil;
import jakarta.annotation.Resource;
import java.io.File;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/22 20:11
 */
// @Service
@DubboService
public class InquirySignatureImageApiImpl implements InquirySignatureImageApi {

    @Resource
    protected FileApi fileApi;

    // public static void main(String[] args) {
    //
    //     File destFile = FileUtil.touch(System.getProperty("java.io.tmpdir") + File.separator + "signatureImage" + File.separator + IdUtil.fastSimpleUUID() + ".jpg");
    //
    //     File file = ImageUtil.mergeImage(destFile, "https://files.test.ybm100.com/INVT/Lzinq/20250422/37cff74b11e489a61059541f11f819037e1e400b51f1edc8fd317f32e652be4e.jpg"
    //         , "https://files.test.ybm100.com/INVT/Lzinq/20250423/f206018cf7bee265336e055098778484b68c9f8a8cf57498ab2b39cf9543f654.png"
    //         , 1f, 100000, 100000, null, 120, 50);
    //
    //     System.out.println(file);
    //
    // }

    @Override
    public String signatureImageMerge(InquirySignatureImageDto dto) {

        File destFile = FileUtil.touch(System.getProperty("java.io.tmpdir") + File.separator + "signatureImage" + File.separator + IdUtil.fastSimpleUUID() + ".jpg");
        try {
            File file = ImageUtil.mergeImage(destFile, dto.getSourceUrl(), dto.getMergeUrl(), dto.getAlpha(), dto.getX(), dto.getY(), dto.getOffset(), dto.getWidth(), dto.getHeight());
            if (file == null) {
                return null;
            }
            return fileApi.createFile(FileUtil.readBytes(file));
        } finally {
            FileUtil.del(destFile);
        }

    }
}
