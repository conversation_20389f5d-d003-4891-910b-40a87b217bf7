package cn.iocoder.yudao.module.system.framework.sms.core.client.dto;

import cn.iocoder.yudao.framework.common.core.KeyValue;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/18 9:40
 */
@Data
@Accessors(chain = true)
public class SmsSendMessageDTO implements Serializable {

    /**
     * 短信日志编号
     */
    private Long logId;
    /**
     * 手机号
     */
    private String mobile;
//    /**
//     * 短信渠道编号
//     */
//    private Long channelId;
//    /**
//     * 短信 API 的模板编号
//     */
//    private String apiTemplateId;
    /**
     * 短信模板参数
     */
    private List<KeyValue<String, Object>> templateParams;

    /**
     * 短信模板DO
     */
    private SmsTemplateRespDTO smsTemplate;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 原始参数
     */
    private Map<String, Object> templateParamsMap;

}
