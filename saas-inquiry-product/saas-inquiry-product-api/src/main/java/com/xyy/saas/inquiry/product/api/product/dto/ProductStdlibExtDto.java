package com.xyy.saas.inquiry.product.api.product.dto;

import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.xyy.saas.inquiry.annotation.FieldCompare;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 商品标准库 DTO
 */
@Schema(description = "商品标准库 扩展信息 DTO")
@Data
@Accessors(chain = true)
public class ProductStdlibExtDto implements Serializable, Cloneable {

    @Schema(description = "一级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "一级分类")
    private Long firstCategoryId;
    @Schema(description = "二级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "二级分类")
    private Long secondCategoryId;
    @Schema(description = "三级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "三级分类")
    private Long thirdCategoryId;
    @Schema(description = "四级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "四级分类")
    private Long fourthCategoryId;
    @Schema(description = "五级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "五级分类")
    private Long fiveCategoryId;
    @Schema(description = "六级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @FieldCompare(description = "六级分类")
    private Long sixCategoryId;

    /**
     * 商品封面图
     */
    @Schema(description = "商品封面图", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @FieldCompare(description = "商品封面图")
    private List<String> coverImages;
    /**
     * 商品外包装图
     */
    @Schema(description = "商品外包装图", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @FieldCompare(description = "商品外包装图")
    private List<String> outerPackageImages;
    /**
     * 商品说明书图
     */
    @Schema(description = "商品说明书图", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @FieldCompare(description = "商品说明书图")
    private List<String> instructionImages;

    /**
     * 数据迁移
     * @param old
     * @return
     */
    public void migrate(ProductStdlibExtDto old) {
        if (old == null) {
            return;
        }
        if (firstCategoryId == null) {
            this.firstCategoryId = old.firstCategoryId;
        }
        if (secondCategoryId == null) {
            this.secondCategoryId = old.secondCategoryId;
        }
        if (thirdCategoryId == null) {
            this.thirdCategoryId = old.thirdCategoryId;
        }
        if (fourthCategoryId == null) {
            this.fourthCategoryId = old.fourthCategoryId;
        }
        if (fiveCategoryId == null) {
            this.fiveCategoryId = old.fiveCategoryId;
        }
        if (sixCategoryId == null) {
            this.sixCategoryId = old.sixCategoryId;
        }
        if (this.coverImages == null) {
            this.coverImages = old.coverImages;
        }
        if (this.outerPackageImages == null) {
            this.outerPackageImages = old.outerPackageImages;
        }
        if (this.instructionImages == null) {
            this.instructionImages = old.instructionImages;
        }
    }

    @Override
    public ProductStdlibExtDto clone() {
        try {
            ProductStdlibExtDto clone = (ProductStdlibExtDto) super.clone();
            // 复制此处的可变状态，这样此克隆就不能更改初始克隆的内部项
            clone.coverImages = this.coverImages == null ? null : this.coverImages.stream().toList();
            clone.outerPackageImages = this.outerPackageImages == null ? null : this.outerPackageImages.stream().toList();
            clone.instructionImages = this.instructionImages == null ? null : this.instructionImages.stream().toList();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError("Clone Not Supported: {}", e);
        }
    }
}