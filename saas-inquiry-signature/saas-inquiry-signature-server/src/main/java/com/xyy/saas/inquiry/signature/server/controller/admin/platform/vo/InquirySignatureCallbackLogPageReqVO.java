package com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 签章回调日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquirySignatureCallbackLogPageReqVO extends PageParam {

    @Schema(description = "签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "类型：事件ID", example = "1")
    private String type;

    @Schema(description = "业务id", example = "9246")
    private String bizId;

    @Schema(description = "具体事件的请求参数，json字符串")
    private String bizContent;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}