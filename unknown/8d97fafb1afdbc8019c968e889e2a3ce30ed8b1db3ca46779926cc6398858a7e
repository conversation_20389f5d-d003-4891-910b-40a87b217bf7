package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @Auther: xucao
 * @Date: 2025/1/08 15:06
 * @Description: 接诊大厅选取类型枚举
 */
@Getter
public enum ReceptionAreaSelectTypeEnum {
    INQUIRY_DEPT(0, "根据问诊科室选择"),
    DEFAULT_DEPT(1, "根据兜底科室选择");

    private int code;
    private String desc;

    ReceptionAreaSelectTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
