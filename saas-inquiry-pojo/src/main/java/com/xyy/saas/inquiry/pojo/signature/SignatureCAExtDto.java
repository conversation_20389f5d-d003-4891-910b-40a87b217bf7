package com.xyy.saas.inquiry.pojo.signature;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/06 14:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "签章CA扩展信息ext")
public class SignatureCAExtDto implements java.io.Serializable {

    /**
     * 签章平台配置id
     */
    private Integer signaturePlatformConfigId;

    /**
     * 实名认证状态 0: 待认证，1: 认证完成，2: 认证失败
     */
    private Integer certifyStatus;
    /**
     * 免签授权状态 0: 未授权，1: 已授权，
     */
    private Integer authorizeFreeSignStatus;
    /**
     * 免签授权截止时间
     */
    private LocalDateTime authorizeFreeSignDdl;

}
