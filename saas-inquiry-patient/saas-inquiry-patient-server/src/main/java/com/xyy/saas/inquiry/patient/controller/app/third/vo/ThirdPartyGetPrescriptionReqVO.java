package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 三方获取处方信息入参对象
 *
 * <AUTHOR>
 * @Date 6/11/24 11:02 AM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "app侧 - 三方预问诊处方信息")
public class ThirdPartyGetPrescriptionReqVO {

    @Schema(description = "预处方id")
    private Long preInquiryId;
}
