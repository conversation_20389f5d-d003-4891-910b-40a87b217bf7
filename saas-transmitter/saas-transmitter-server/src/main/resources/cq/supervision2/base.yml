#基础过滤 29999
preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data['hospitalPref'],'H100008','H100009')
pricingHospitalPref: H100008,H100009
preParameter:
  nodes:
    - prescriptionCa
preParameterFilter:
  condition: T(cn.hutool.core.collection.CollUtil).size(aux['prescriptionCa']) >= 2


# Q299  30000 挂号
preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data['hospitalPref'],'H100008','H100009')
executionTime:
  onlyTask: true #仅上传任务
postParameter:
  nodes:
    - operateUserInfo
dependency:
  downstreamNodes:
    - 30001

# Q341  30001 病例
preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data['hospitalPref'],'H100008','H100009')
postParameter:
  nodes:
    - clinicalCase
    - doctorInfo
    - pharmacistInfo
#    - medicalRegistrationInfo
dependency:
  downstreamNodes:
    - 30002

  # Q310  30002 点评
preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data['hospitalPref'],'H100008','H100009')
postParameter:
  nodes:
    - doctorInfo
    - pharmacistInfo
    #    - medicalRegistrationInfo
    - prescriptionDetail
    - clinicalCase
    - prescriptionCa
    - inquiryDetailInfo
    - stdlibProduct
dependency:
  downstreamNodes:
    - 30003

  # Q300  30003 结算
preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data['hospitalPref'],'H100008','H100009')
postParameter:
  nodes:
    - doctorInfo
    - pharmacistInfo
    - medicalRegistrationInfo
    - prescriptionDetail
    - clinicalCase
    - operateUserInfo
    - inquiryDetailInfo
dependency:
  downstreamNodes:
    - 30004

  # Q430  30004 派药
preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).containsAny(data['hospitalPref'],'H100008','H100009')