package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorVideoDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 医生录屏记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryDoctorVideoService {

    /**
     * 创建医生录屏记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryDoctorVideo(@Valid InquiryDoctorVideoSaveReqVO createReqVO);

    /**
     * 更新医生录屏记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryDoctorVideo(@Valid InquiryDoctorVideoSaveReqVO updateReqVO);

    /**
     * 删除医生录屏记录
     *
     * @param id 编号
     */
    void deleteInquiryDoctorVideo(Long id);

    /**
     * 获得医生录屏记录
     *
     * @param id 编号
     * @return 医生录屏记录
     */
    InquiryDoctorVideoDO getInquiryDoctorVideo(Long id);

    /**
     * 获取医生录屏问诊记录列表
     *
     * @param reqVO
     * @return
     */
    List<InquiryDoctorVideoDO> selectByDoctorPref(String doctorPref);

    /**
     * 获得医生录屏记录分页
     *
     * @param pageReqVO 分页查询
     * @return 医生录屏记录分页
     */
    PageResult<InquiryDoctorVideoDO> getInquiryDoctorVideoPage(InquiryDoctorVideoPageReqVO pageReqVO);

    /**
     * 根据pref查询视频地址
     * @param pref 编码
     * @return 视频地址
     */
    String selectVideoUrlByPref(String pref);
}