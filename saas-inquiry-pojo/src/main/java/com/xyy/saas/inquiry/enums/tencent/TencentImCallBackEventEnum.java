package com.xyy.saas.inquiry.enums.tencent;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Author: xucao
 * @Description:腾讯IM回调事件枚举类
 */
@Getter
@RequiredArgsConstructor
public enum TencentImCallBackEventEnum{
    C2C_CALLBACK_AFTER_SEND_MSG("C2C.CallbackAfterSendMsg", "发单聊消息之后回调"),
    C2C_CALLBACK_AFTER_MSG_REPORT("C2C.CallbackAfterMsgReport", "单聊消息已读上报后回调");


    private final String code;

    private final String desc;

    public static TencentImCallBackEventEnum fromCode(String code) {
        return Arrays.stream(values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
    }

}
