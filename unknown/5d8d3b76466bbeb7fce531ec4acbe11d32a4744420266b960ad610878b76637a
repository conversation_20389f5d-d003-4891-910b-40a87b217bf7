package com.xyy.saas.inquiry.product.server.dal.dataobject.gsp;

import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.util.List;

/**
 * 售价调整单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_product_price_adjustment_record", autoResultMap = true)
@KeySequence("saas_product_price_adjustment_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductPriceAdjustmentRecordDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 单据编号
     */
    private String pref;
    /**
     * 适用门店
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<Long> applicableTenantIds;
    /**
     * 调价原因
     */
    private String adjustmentReason;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 备注
     */
    private String remark;

}