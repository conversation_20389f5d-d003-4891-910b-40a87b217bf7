<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.transfer.ProductTransferRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="product_pref" property="productPref" jdbcType="VARCHAR"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
    <result column="source_tenant_id" property="sourceTenantId" jdbcType="BIGINT"/>
    <result column="target_tenant_id" property="targetTenantId" jdbcType="BIGINT"/>
    <result column="show_pref" property="showPref" jdbcType="VARCHAR"/>
    <result column="mnemonic_code" property="mnemonicCode" jdbcType="VARCHAR"/>
    <result column="common_name" property="commonName" jdbcType="VARCHAR"/>
    <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>
    <result column="spec" property="spec" jdbcType="VARCHAR"/>
    <result column="barcode" property="barcode" jdbcType="VARCHAR"/>
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR"/>
    <result column="approval_number" property="approvalNumber" jdbcType="VARCHAR"/>
    <result column="ext" property="ext" jdbcType="VARCHAR" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="result" property="result" jdbcType="VARCHAR"/>
    <result column="disable" property="disable" jdbcType="BIT"/>
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="BIT" />
  </resultMap>

  <resultMap id="BaseResultMap2" type="com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="product_pref" property="productPref" jdbcType="VARCHAR"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
    <result column="source_tenant_id" property="sourceTenantId" jdbcType="BIGINT"/>
    <result column="target_tenant_id" property="targetTenantId" jdbcType="BIGINT"/>
    <result column="show_pref" property="showPref" jdbcType="VARCHAR"/>
    <result column="mnemonic_code" property="mnemonicCode" jdbcType="VARCHAR"/>
    <result column="common_name" property="commonName" jdbcType="VARCHAR"/>
    <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>
    <result column="spec" property="spec" jdbcType="VARCHAR"/>
    <result column="barcode" property="barcode" jdbcType="VARCHAR"/>
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR"/>
    <result column="approval_number" property="approvalNumber" jdbcType="VARCHAR"/>
    <result column="ext" property="ext" jdbcType="VARCHAR" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="result" property="result" jdbcType="VARCHAR"/>
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="sourceTenantName" property="sourceTenantName" jdbcType="VARCHAR" />
    <result column="sourceTenantPref" property="sourceTenantPref" jdbcType="VARCHAR" />
    <result column="sourceTenantProvince" property="sourceTenantProvince" jdbcType="VARCHAR" />
    <result column="sourceTenantCity" property="sourceTenantCity" jdbcType="VARCHAR" />
    <result column="sourceTenantArea" property="sourceTenantArea" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List">
    id, product_pref, type, source_tenant_id, target_tenant_id,
    show_pref, mnemonic_code, common_name, brand_name, spec, barcode, manufacturer, approval_number, ext,
    status, result, disable, remark, create_time, update_time, creator, updater, deleted
  </sql>

  <sql id="Where_Search_TransferRecord_AS_t">
    <if test="param.idList != null and param.idList.size() > 0">
      AND t.id IN
      <foreach collection="param.idList" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    <if test="param.productPrefList != null and param.productPrefList.size() > 0">
      AND t.product_pref IN
      <foreach collection="param.productPrefList" item="productPref" open="(" separator="," close=")">
        #{productPref}
      </foreach>
    </if>
    <if test="param.mixedQuery != null and param.mixedQuery != ''">
      AND match(t.show_pref, t.common_name, t.brand_name, t.barcode, t.mnemonic_code, t.approval_number, t.manufacturer) against( #{param.mixedQuery} in BOOLEAN MODE )
    </if>
    <if test="param.barcode != null and param.barcode != '' and param.barcode != '0'">
      AND t.barcode = #{param.barcode}
    </if>
    <if test="param.commonName != null">
      AND t.common_name = #{param.commonName}
    </if>
    <if test="param.brandName != null">
      AND t.brand_name = #{param.brandName}
    </if>
    <if test="param.spec != null">
      AND t.spec = #{param.spec}
    </if>
    <if test="param.approvalNumber != null">
      AND t.approval_number = #{param.approvalNumber}
    </if>
    <if test="param.manufacturer != null">
      AND t.manufacturer = #{param.manufacturer}
    </if>
    <if test="param.sourceTenantId != null">
      AND t.source_tenant_id = #{param.sourceTenantId}
    </if>
    <if test="param.targetTenantId != null">
      AND t.target_tenant_id = #{param.targetTenantId}
    </if>
    <if test="param.statusList != null and param.statusList.size() > 0">
      AND t.status IN
      <foreach collection="param.statusList" item="status" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="param.createTime != null and param.createTime.length > 0">
      AND t.create_time BETWEEN #{param.createTime[0],javaType=java.time.LocalDateTime}
      AND #{param.createTime[1],javaType=java.time.LocalDateTime}
    </if>
  </sql>

  <sql id="Where_Search_Tenant_AS_s">
    <if test="param.provinceCode != null and param.provinceCode != ''">
      and s.province_code = #{param.provinceCode}
    </if>
    <if test="param.cityCode != null and param.cityCode != ''">
      and s.city_code = #{param.cityCode}
    </if>
    <if test="param.areaCode != null and param.areaCode != ''">
      and s.area_code = #{param.areaCode}
    </if>
  </sql>

  <select id="listTransferRecord" resultMap="BaseResultMap">
    SELECT t.* FROM saas_product_transfer_record t
    WHERE t.deleted = 0 and t.disable = 0
      AND t.type = #{type}
      <include refid="Where_Search_TransferRecord_AS_t" />
    ORDER BY t.create_time DESC
  </select>

  <select id="getLatestPresentPage" resultMap="BaseResultMap2">
    SELECT t.*,
      s.name AS sourceTenantName,
      s.pref AS sourceTenantPref,
      s.province AS sourceTenantProvince,
      s.city AS sourceTenantCity,
      s.area AS sourceTenantArea
    FROM saas_product_transfer_record t
      JOIN system_tenant s on t.source_tenant_id = s.id
    WHERE t.deleted = 0 and t.disable = 0
      AND t.type = ${@com.xyy.saas.inquiry.product.enums.ProductTransferTypeEnum@REPORT_MID.code}
      <include refid="Where_Search_TransferRecord_AS_t" />
      <include refid="Where_Search_Tenant_AS_s" />
    ORDER BY t.create_time DESC
  </select>

  <select id="getLatestPresentGroupByStatus" resultType="hashmap">
    SELECT status, count(*) AS count
    FROM saas_product_transfer_record t
    WHERE t.deleted = 0 and t.disable = 0
      AND t.type = ${@com.xyy.saas.inquiry.product.enums.ProductTransferTypeEnum@REPORT_MID.code}
      <include refid="Where_Search_TransferRecord_AS_t" />
    group by t.status
  </select>

</mapper>