package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorAuditedRecordDO;
import jakarta.validation.Valid;


/**
 * 医生审核记录 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorAuditedRecordService {

    /**
     * 创建医生审核记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoctorAuditedRecord(@Valid DoctorAuditedRecordSaveReqVO createReqVO);

    /**
     * 更新医生审核记录
     *
     * @param updateReqVO 更新信息
     */
    void updateDoctorAuditedRecord(@Valid DoctorAuditedRecordSaveReqVO updateReqVO);

    /**
     * 删除医生审核记录
     *
     * @param id 编号
     */
    void deleteDoctorAuditedRecord(Long id);

    /**
     * 获得医生审核记录
     *
     * @param id 编号
     * @return 医生审核记录
     */
    DoctorAuditedRecordDO getDoctorAuditedRecord(Long id);

    /**
     * 获得医生审核记录分页
     *
     * @param pageReqVO 分页查询
     * @return 医生审核记录分页
     */
    PageResult<DoctorAuditedRecordDO> getDoctorAuditedRecordPage(DoctorAuditedRecordPageReqVO pageReqVO);

}