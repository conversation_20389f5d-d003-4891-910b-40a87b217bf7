package com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 问诊配置选项 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_option_config")
@KeySequence("saas_inquiry_option_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryOptionConfigDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 目标类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum}
     */
    private Integer targetType;
    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    private Integer optionType;
    /**
     * 区域编码或者租户id
     */
    private Long targetId;
    /**
     * 区域名称或者租户名称
     */
    private String targetName;
    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 配置选项类型
     */
    private String optionName;
    /**
     * 配置值
     */
    private String optionValue;
    /**
     * 配置描述
     */
    private String description;

    /**
     * 是否使用此配置 0开启，1关闭 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer used;


    /**
     * 处方拓展字段
     */
    private String ext;

    public InquiryOptionConfigDO setterOptionValue(Supplier<Object> setter, boolean isNullDelete) {
        return Optional.ofNullable(setter.get()).map(String::valueOf)
            .map(this::setOptionValue).orElseGet(() -> {
                if (isNullDelete) {
                    this.setDeleted(true);
                    this.setOptionValue("");
                }
                return this;
            });
    }

    public InquiryOptionConfigDO setterExt(Supplier<Map> setter, boolean isNullDelete) {
        return Optional.ofNullable(setter.get()).map(JsonUtils::toJsonString)
            .map(this::setExt).orElseGet(() -> {
                if (isNullDelete) {
                    this.setDeleted(true);
                    this.setExt(null);
                }
                return this;
            });
    }

    /**
     * 获取优先级
     *
     * @param optionTypeEnum
     * @return
     */
    public int priority(InquiryOptionTypeEnum optionTypeEnum) {
        if (optionTypeEnum == null) {
            return Integer.MAX_VALUE;
        }
        InquiryTargetTypeEnum targetTypeEnum = InquiryTargetTypeEnum.fromType(this.getTargetType());
        if (targetTypeEnum == null) {
            return Integer.MAX_VALUE;
        }
        return Arrays.asList(optionTypeEnum.getTargetTypePriorities()).indexOf(targetTypeEnum);
    }

}