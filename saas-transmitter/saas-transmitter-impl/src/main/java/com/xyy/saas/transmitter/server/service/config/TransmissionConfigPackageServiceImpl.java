package com.xyy.saas.transmitter.server.service.config;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_PACKAGE_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_PACKAGE_NOT_EXISTS;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_RELATION_PARENT;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_PROD;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_USED;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.ServiceEnvEnum;
import com.xyy.saas.inquiry.util.UserUtil;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigPackageDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackagePageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageRespVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.convert.config.TransmissionConfigPackageConvert;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigPackageMapper;
import com.xyy.saas.transmitter.server.service.servicepack.TransmissionServicePackService;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

/**
 * 协议配置包 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransmissionConfigPackageServiceImpl
    extends ServiceImpl<TransmissionConfigPackageMapper, TransmissionConfigPackageDO>
    implements TransmissionConfigPackageService {

    @Resource
    private TransmissionConfigPackageMapper configPackageMapper;

    @Resource
    private TransmissionConfigItemService configItemService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private TransmissionServicePackService transmissionServicePackService;

    @Override
    public Integer createTransmissionConfigPackage(TransmissionConfigPackageSaveReqVO createReqVO) {
        // 设置版本号
        createReqVO.setVersion(TransmissionConfigPackageConvert.INSTANCE.generateCurrentVersion());

        // 校验是否存在相同配置包
        validateConfigPackageExists(null, createReqVO.getName(),
            createReqVO.getOrganType(), createReqVO.getVersion());

        // 插入
        TransmissionConfigPackageDO transmissionConfigPackage = TransmissionConfigPackageConvert.INSTANCE
            .convert(createReqVO);
        configPackageMapper.insert(transmissionConfigPackage);
        // 返回
        return transmissionConfigPackage.getId();
    }

    @Override
    public void updateTransmissionConfigPackage(TransmissionConfigPackageSaveReqVO updateReqVO) {
        // 校验存在
        TransmissionConfigPackageDO configPackageDO = validateTransmissionConfigPackageExists(updateReqVO.getId());

        // 校验是否存在上线的配置包，或者 如果是禁用，判断已经关联服务包
        validateTransmissionServicePack(updateReqVO, configPackageDO);

        // 校验是否存在相同配置包
        validateConfigPackageExists(updateReqVO.getId(), updateReqVO.getName(),
            updateReqVO.getOrganType(), updateReqVO.getVersion());

        // 更新
        TransmissionConfigPackageDO updateObj = TransmissionConfigPackageConvert.INSTANCE.convert(updateReqVO);
        configPackageMapper.updateById(updateObj);
        configPackageMapper.update(new UpdateWrapper<TransmissionConfigPackageDO>().set("parent_package_id", updateReqVO.getParentPackageId()).eq("id", updateReqVO.getId()));
    }


    private void validateTransmissionServicePack(TransmissionConfigPackageSaveReqVO updateReqVO, TransmissionConfigPackageDO configPackageDO) {

        TransmissionServicePackPageReqVO reqVO = TransmissionServicePackPageReqVO.builder().configPackageId(updateReqVO.getId()).disable(false).build();
        // 禁用提示：该配置包已被x个服务包使用，无法禁用！
        if (!Objects.equals(configPackageDO.getDisable(), updateReqVO.getDisable()) && updateReqVO.getDisable()) {
            Long count = transmissionServicePackService.selectCountServicePack(reqVO);
            if (count > 0) {
                throw exception(TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_USED, count);
            }
            // 当前配置包已成为其他父类 配置
            if (configPackageMapper.selectConfigCount(TransmissionConfigPackagePageReqVO.builder().parentPackageId(updateReqVO.getId()).disable(false).build()) > 0) {
                throw exception(TRANSMISSION_CONFIG_RELATION_PARENT);
            }
        }

        // 编辑提示：该配置包关联的服务包已上线,不可操作
        TenantTransmissionServicePackRespDTO servicePackRespDTO = transmissionServicePackService.selectOneServicePack(reqVO.setEnv(ServiceEnvEnum.PROD.getCode()));
        if (servicePackRespDTO != null) {
            throw exception(TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_PROD);
        }


    }

    @Override
    public void deleteTransmissionConfigPackage(Integer id) {
        // 校验存在
        validateTransmissionConfigPackageExists(id);
        // 删除
        configPackageMapper.deleteById(id);
    }

    private TransmissionConfigPackageDO validateTransmissionConfigPackageExists(Integer id) {
        TransmissionConfigPackageDO configPackageDO = configPackageMapper.selectById(id);
        if (configPackageDO == null) {
            throw exception(TRANSMISSION_CONFIG_PACKAGE_NOT_EXISTS);
        }
        return configPackageDO;
    }

    private void validateConfigPackageExists(Integer id, String name, Integer organType, Long version) {
        TransmissionConfigPackageDO existingPackage = configPackageMapper
            .selectByNameAndOrganTypeAndVersion(name, organType, version);
        if (existingPackage != null && !existingPackage.getId().equals(id)) {
            throw exception(TRANSMISSION_CONFIG_PACKAGE_EXISTS, name, version);
        }

    }

    @Override
    public TransmissionConfigPackageDO getTransmissionConfigPackage(Integer id) {
        return configPackageMapper.selectById(id);
    }


    @Override
    public List<TransmissionConfigPackageDO> getTransmissionConfigPackages(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return List.of();
        }
        return configPackageMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<TransmissionConfigPackageRespVO> getTransmissionConfigPackagePage(
        TransmissionConfigPackagePageReqVO pageReqVO) {

        PageResult<TransmissionConfigPackageRespVO> pageResult = TransmissionConfigPackageConvert.INSTANCE
            .convertPage(configPackageMapper.selectTransmissionConfigPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO));
        // 填充配置包数量
        fillConfigItemCount(pageResult.getList());

        // 补充用户信息
        UserUtil.fillUserInfo(pageResult.getList(), adminUserApi::getUserNameMap);
        return pageResult;
    }

    private void fillConfigItemCount(List<TransmissionConfigPackageRespVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Integer> configPackIds = cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(list, TransmissionConfigPackageRespVO::getId);
        Map<Integer, Long> itemCountMap = configItemService.selectItemCountByPackId(configPackIds, false);
        for (TransmissionConfigPackageRespVO respVO : list) {
            respVO.setItemCount(itemCountMap.get(respVO.getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer copyTransmissionConfigPackage(Integer id) {
        // 1. 查询原配置包
        TransmissionConfigPackageDO originalPackage = configPackageMapper.selectById(id);
        if (originalPackage == null) {
            throw exception(TRANSMISSION_CONFIG_PACKAGE_NOT_EXISTS);
        }

        // 2. 创建新的配置包
        TransmissionConfigPackageDO newPackage = TransmissionConfigPackageConvert.INSTANCE
            .copyConfigPackage(originalPackage);

        // 校验是否存在相同配置包
        validateConfigPackageExists(null, originalPackage.getName(),
            originalPackage.getOrganType(), newPackage.getVersion());

        // 3. 插入新配置包
        configPackageMapper.insert(newPackage);

        // 4. 复制配置项
        configItemService.copyConfigItems(id, newPackage.getId());

        return newPackage.getId();
    }

    /**
     * 获取完整的配置包Map
     *
     * @param configPackageIds       配置包ID列表
     * @param nodeType               节点类型
     * @param validateProtocolConfig 是否需要验证存在协议配置
     * @return 配置包ID到配置包信息的映射
     */
    @Override
    public Map<Integer, TransmissionConfigPackageDTO> getConfigPackageMap(List<Integer> configPackageIds,
        NodeTypeEnum nodeType, boolean validateProtocolConfig) {
        if (CollectionUtils.isEmpty(configPackageIds)) {
            return new HashMap<>();
        }

        // 1. 根据过滤符合条件的配置项
        Map<Integer, List<TransmissionConfigItemDTO>> configItemMap = configItemService
            .getItemMapByNodeType(configPackageIds, nodeType, validateProtocolConfig);
        if (CollectionUtils.isEmpty(configItemMap)) {
            log.warn("[getConfigItemsByPackageIds][配置包({})下未找到业务类型为({})的配置项]", configPackageIds, nodeType.getDesc());
            return new HashMap<>();
        }

        // 2. 获取配置包
        List<Integer> validPackageIds = configItemMap.keySet().stream().toList();
        List<TransmissionConfigPackageDO> configPackages = configPackageMapper.selectList(
            TransmissionConfigPackagePageReqVO.builder()
                .ids(validPackageIds)
                .disable(false)
                .build());

        if (CollectionUtils.isEmpty(configPackages)) {
            log.warn("[getValidConfigPackages][配置包({})不存在或已禁用]", configPackageIds);
            return new HashMap<>();
        }

        // 3. 获取公共配置项
        Map<Integer, List<TransmissionConfigItemDTO>> commonConfigItemMap = configItemService
            .getItemMapByNodeType(configPackageIds, NodeTypeEnum.COMMON, false);

        // 4. 组装并返回完整的配置包
        return TransmissionConfigPackageConvert.INSTANCE.assembleConfigPackages(
            configPackages,
            configItemMap,
            commonConfigItemMap);
    }

}