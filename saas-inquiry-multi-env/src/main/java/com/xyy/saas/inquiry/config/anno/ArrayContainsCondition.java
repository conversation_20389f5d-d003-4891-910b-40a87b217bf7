package com.xyy.saas.inquiry.config.anno;


import jakarta.annotation.Nonnull;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class ArrayContainsCondition implements Condition {
    @Override
    public boolean matches(@Nonnull ConditionContext context, AnnotatedTypeMetadata metadata) {
        var attrs = metadata.getAnnotationAttributes(
            ConditionalOnArrayContainsProperty.class.getName());

        String prefix = "";
        if (attrs != null) {
            prefix = StringUtils.hasText((String) attrs.get("prefix")) ? attrs.get("prefix") + "." : "";
        }

        String propertyName = "";
        if (attrs != null) {
            propertyName = prefix + attrs.get("value");
        }
        String targetValue = null;
        if (attrs != null) {
            targetValue = (String) attrs.get("havingValue");
        }

        // 直接使用 Spring 的 Binder API
        Binder binder = Binder.get(context.getEnvironment());
        String[] values = binder.bind(
            propertyName,
            Bindable.of(String[].class)
        ).orElseGet(() -> new String[0]);

        return values != null && Arrays.asList(values).contains(targetValue);
    }

}

