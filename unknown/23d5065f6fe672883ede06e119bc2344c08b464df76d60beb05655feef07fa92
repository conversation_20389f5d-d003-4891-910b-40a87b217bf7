package com.xyy.saas.inquiry.enums.signature;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 合同状态
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum ContractStatusEnum {

    DRAFT(0, "初始状态/草稿"),

    SIGNING(1, "签署中"),

    COMPLETE(2, "已完成"),

    ABNORMAL(3, "异常终止"),

    TERMINATED(4, "已作废"),

    DELETE(5, "已删除"),

    EXPIRED(6, "已过期"),

    UNKNOWN(-1, "未知"),
    ;

    private final int code;

    private final String desc;

    public static ContractStatusEnum fromStatusCode(int code) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == code)
            .findFirst()
            .orElse(UNKNOWN);
    }


}
