package com.xyy.saas.inquiry.pharmacist.server.config;

import com.xyy.saas.inquiry.config.webclient.ForwardWebClientConfig;
import com.xyy.saas.inquiry.config.webclient.InquiryForwardProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 转发旧问诊服务配置
 *
 * @Author: cxy
 */
@Slf4j
@Configuration
public class PharmacistForwardWebClientConfig {

    @Bean
    public InquiryPharmacistForwardClient inquiryPharmacistForwardClient(@Qualifier("forwardWebClient") WebClient webClient, InquiryForwardProperties inquiryForwardProperties) {
        return ForwardWebClientConfig.getHttpServiceClient(webClient, inquiryForwardProperties, InquiryPharmacistForwardClient.class);
    }
}
