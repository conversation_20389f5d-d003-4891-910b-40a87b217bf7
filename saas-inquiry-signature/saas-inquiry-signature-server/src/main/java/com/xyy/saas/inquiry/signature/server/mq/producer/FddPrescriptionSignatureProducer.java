package com.xyy.saas.inquiry.signature.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.signature.server.mq.message.FddPrescriptionSignatureEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 法大大签章处方mq
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = FddPrescriptionSignatureEvent.TOPIC
)
public class FddPrescriptionSignatureProducer extends EventBusRocketMQTemplate {


}
