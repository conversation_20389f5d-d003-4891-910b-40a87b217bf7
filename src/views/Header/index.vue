<template>
  <div class="header">
    <div class="left-header">
      <div class="merchant-select-container" v-if="user.drugStore || user.pharmacist">
        <!-- 门店选择框 -->
        <div class="merchant-select-title">门店名称</div>
        <el-select
          v-model="selectedStoreId"
          filterable
          placeholder="请选择门店"
          @change="selectStore"
        >
          <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </div>

    <div class="right-header">
      <div class="flex justify-end items-center">
        <!-- 二维码 -->
        <el-dropdown
          trigger="click"
          :hide-on-click="false"
          class="app-dropdown mr-10px"
          @visible-change="onChanegeAppDrowdown"
        >
          <div class="el-dropdown-link code-container flex justify-center items-center">
            <img src="@/assets/手机.png" />
            <span
              :class="{ 'is-show-code': isShowApp }"
              style="
                text-align: justify; /* 使文本两端对齐 */
                overflow-wrap: break-word; /* 强制文本换行 */
                word-wrap: break-word;
                display: inline-block;
              "
              >App下载</span
            >
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="code" class="header-code-menu">
                <div class="header-qr-code">
                  <div class="img-container">
                    <img :src="qrCodeUrl" />
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 药师，上下班 -->
        <template v-if="user.pharmacist">
          <el-button
            type="primary"
            v-if="pharmacistState.onlineStatus === 0"
            @click="handlePharmacistReceipt"
            >上班打卡</el-button
          >
          <el-button
            type="primary"
            v-if="pharmacistState.onlineStatus === 1"
            @click="handlePharmacistReceipt"
            >下班打卡</el-button
          >
        </template>

        <!-- 账号管理 -->
        <el-dropdown @visible-change="showDrowdown" trigger="click" @command="handleCommand">
          <span class="el-dropdown-link pr-20px flex justify-end items-center">
            <img :src="avatarUrl || defaultAvatar" style="width: 28px; height: 28px" />
            <span class="account-span ml-5px mr-5px" :title="userInfo.account">{{
              userInfo.account
            }}</span>
            <!-- <img :src="dropdownStatus ? ArrowTop : ArrowBottom" /> -->
            <el-icon><component :is="dropdownStatus ? ArrowUp : ArrowDown" /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu class="account-dropdown-menu">
              <el-dropdown-item command="个人中心">
                <!-- <img src="@/assets/header/account-manage.png" /> -->
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="修改指纹" v-if="user.pharmacist">
                <!-- <img src="@/assets/header/fingerprint.png" /> -->
                修改指纹
              </el-dropdown-item>
              <el-dropdown-item command="指纹审方" v-if="user.pharmacist">
                <div class="w-100% flex justify-center">
                  <!-- :before-change="onBeforeValidFingerSwitchChange" -->
                  <el-switch
                    v-model="pharmacistState.fingerPrintAudit"
                    @change="onValidFingerSwitchChange"
                /></div>
              </el-dropdown-item>
              <el-dropdown-item command="退出登录">
                <!-- <img src="@/assets/header/logout.png" /> -->
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>

  <!-- 上下班打卡组件 -->
  <WorkClockDialog ref="refWorkClockDialog" @success="emitSuccessWorkClockDialog" />

  <!-- 录入指纹组件 || 修改指纹 -->
  <AddUpdateFinger ref="refAddUpdateFinger" @success="emitSuccessAddUpdateFinger" />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { CACHE_KEY, useCache, deleteUserCache } from '@/hooks/web/useCache'
import {
  getAccessToken,
  getRefreshToken,
  getTenantId,
  removeToken,
  setToken,
  setTenantId,
  isSystemTenant
} from '@/utils/auth'
import { ElMessage } from 'element-plus'
import * as StoreApi from '@/api/system/store'
import { InquiryPortalPharmacistApi } from '@/api/inquiry/portalPharmacist/index'
import * as ConfigApi from '@/api/infra/config'

import defaultAvatar from '@/assets/header/default-avatar.png' // 导入本地图片
import ArrowTop from '@/assets/header/arrow-top.png'
import ArrowBottom from '@/assets/header/arrow-bottom.png'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

import WorkClockDialog from '@/views/inquiry/portalPharmacist/components/WorkClockDialog.vue'
import AddUpdateFinger from '@/views/inquiry/portalPharmacist/components/AddUpFinger.vue'

const router = useRouter()
const userStore = useUserStore()
const tagsViewStore = useTagsViewStore()
const message = useMessage() // 消息弹窗
const { wsCache } = useCache()

const user = wsCache.get(CACHE_KEY.USER)
const user_role = computed(() => {
  if (user.drugStore && !user.pharmacist) {
    // 药店 && !药师
    return 'drugStore'
  } else if (user.pharmacist) {
    // 药师
    return 'pharmacist'
  } else if (user.physician) {
    // 医生
    return 'physician'
  } else {
    return 'drugStore'
  }
})

const storeList = ref([])
const selectedStoreId = ref('')

const avatarUrl = ref('')
const userInfo = ref<any>({})

const isShowApp = ref(false) //  App下载否展示
let qrCodeUrl = ref('')

const dropdownStatus = ref(false)

// 药师
const pharmacistState = reactive({
  onlineStatus: -1, // 药师出诊状态，0停诊；1出诊
  fingerPrintAudit: false, // 药师指纹审核开关，0开启；1关闭
  hasFingerPrint: false, // 是否有指纹信息，true有，fals无
  provinceCode: '' // 执业省份
})
const refWorkClockDialog = ref<any>(null) // 上下班打卡
const refAddUpdateFinger = ref<any>(null) // 录入指纹 || 修改指纹

onMounted(async () => {
  avatarUrl.value = userStore.getUser.avatar // ?userStore.getUser.avatar:wsCache.get(CACHE_KEY.USER).user.avatar其他·用户·第一次登录时用户图像不能·获取到·
  userInfo.value.account = userStore.getUser.nickname

  selectedStoreId.value = getTenantId()

  // 查门店
  queryStoreList()

  // 查询二维码
  queryQrCode()

  // 药师
  if (user.pharmacist) {
    queryPharmacistReceiptInfo()
  }
})

// 查门店
const queryStoreList = async () => {
  let res = await StoreApi.getStoreList(userStore.getUser.id)
  storeList.value = res
}
// 切换门店
const selectStore = async () => {
  let res = await StoreApi.changeStore({
    userId: userStore.getUser.id,
    tenantId: selectedStoreId.value,
    token: getAccessToken()
  })
  //  setToken(res.accessToken)
  setTenantId(res.tenantId)
  setToken({
    accessToken: res.accessToken,
    refreshToken: res.refreshToken
  })
  //重新设置token和门店id后刷新页面
  tagsViewStore.delAllVisitedViews() //清楚tags标签页
  await router.push('/home') //跳转home页面
  router.go(0)
}

// 查询二维码
const queryQrCode = async () => {
  const respQrCodeUrl = await ConfigApi.getConfigKey('inquiry.app.qr.code')
  qrCodeUrl.value = respQrCodeUrl
}
// 二维码下拉
function onChanegeAppDrowdown(command: boolean) {
  isShowApp.value = command
}

// 查询药师信息
const queryPharmacistReceiptInfo = async () => {
  const respData = await InquiryPortalPharmacistApi.queryPharmacistReceipt({
    userId: userStore.getUser.id
  })
  pharmacistState.onlineStatus = respData.onlineStatus
  pharmacistState.fingerPrintAudit = respData.fingerPrintAudit === 0
  pharmacistState.hasFingerPrint = respData.hasFingerPrint
  pharmacistState.provinceCode = respData.provinceCode
}

// 药师上下班打卡
const handlePharmacistReceipt = async () => {
  // 获取用户配置信息
  const userStoreInfo = await InquiryPortalPharmacistApi.getUserInfo()
  // needClockIn,是否需要打卡 (0是 1否)
  if (userStoreInfo && userStoreInfo.needClockIn === 0) {
    // 需要打卡
    if (pharmacistState.hasFingerPrint) {
      // 有指纹，去验证指纹
      const title = `${pharmacistState.onlineStatus === 0 ? '上班打卡' : '下班打卡'}`
      refWorkClockDialog.value.open({ title, type: '' })
    } else {
      // 无指纹，去录入指纹
      refAddUpdateFinger.value.open({ title: '录入指纹', type: 'add' })
    }
  } else {
    // 不需要打卡
    doPharmacistReceipt()
  }
}
// 指纹校验成功回调
const emitSuccessWorkClockDialog = ({ type, userId, tenantId, fingerprintInfo }) => {
  doPharmacistReceipt()
}
// 录入指纹成功回调
const emitSuccessAddUpdateFinger = async ({ type, userId, tenantId, fingerprintInfo }) => {
  if (type === 'add') {
    // 重置标识
    pharmacistState.hasFingerPrint = true
    // 出诊
    doPharmacistReceipt()
  } else if (type === 'update') {
    // 修改指纹成功回调,此处无操作
  }
}
const doPharmacistReceipt = async () => {
  if (pharmacistState.onlineStatus === 0) {
    // 停诊状态，去上班(出诊)
    await InquiryPortalPharmacistApi.startPharmacistReceipt()
    userStore.setPharmacistRefresh(true)
  } else if (pharmacistState.onlineStatus === 1) {
    // 出诊状态，去下班(停诊)
    await InquiryPortalPharmacistApi.stopPharmacistReceipt()
    userStore.setPharmacistRefresh(true)
  }
}
watch(
  () => userStore.getPharmacistRefresh,
  (newValue) => {
    if (newValue) {
      queryPharmacistReceiptInfo()
      setTimeout(() => {
        userStore.setPharmacistRefresh(false)
      }, 0)
    }
  }
)

// 账号管理下拉
function showDrowdown(command: boolean) {
  dropdownStatus.value = command
}

// 个人中心
const handleNg2UserCenter = () => {
  router.push({ path: '/user/profile' })
}

// 修改指纹
const handleUpdateFinger = () => {
  refAddUpdateFinger.value?.open({ title: '修改指纹', type: 'update' })
}

// 是否指纹审方开关
const onBeforeValidFingerSwitchChange = (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    message
      .confirm(`是否 [ ${pharmacistState.fingerPrintAudit ? '关闭' : '打开'} ] 指纹审方?`)
      .then(async () => {
        await InquiryPortalPharmacistApi.updatePharmacistReceipt({
          userId: userStore.getUser.id,
          fingerPrintAudit: pharmacistState.fingerPrintAudit ? 1 : 0, // 是否需要指纹审核
          provinceCode: pharmacistState.provinceCode
        })
        userStore.setPharmacistRefresh(true) // queryPharmacistReceiptInfo()
        message.success(`操作成功`)
        return resolve(true)
      })
      .catch(() => {
        reject(new Error())
      })
  })
}
const onValidFingerSwitchChange = async (switchValue: boolean) => {
  try {
    await InquiryPortalPharmacistApi.updatePharmacistReceipt({
      userId: userStore.getUser.id,
      fingerPrintAudit: switchValue ? 0 : 1, // 是否需要指纹审核
      provinceCode: pharmacistState.provinceCode
    })
    userStore.setPharmacistRefresh(true) // queryPharmacistReceiptInfo()
    message.success(`操作成功`)
  } catch (err) {
    pharmacistState.fingerPrintAudit = !switchValue
    message.error(`操作失败`)
  }
}

// 退出登录
const handleLogout = async () => {
  await message.confirm(`确认要退出登录吗？`, `确认提示`)
  await userStore.loginOut()
  tagsViewStore.delAllViews()
  router.push({
    path: '/login'
  })
}

function handleCommand(command) {
  switch (command) {
    case '个人中心':
      handleNg2UserCenter()
      break
    case '门店管理':
      router.push(`/storeInformation/storeManagementList?type=drugstore`)
      break
    case '门店申请':
      router.push(`/storeInformation/storeApplicant?type=drugstoreSettled`)
      break
    case '修改指纹':
      handleUpdateFinger()
      break
    case '指纹审方':
      break
    case '退出登录':
      handleLogout()
      break
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
.header {
  width: 100%;
  height: 50px;
  color: #333333;
  font-size: 14px;
  transition:
    width 0.4s,
    left 0.4s;
  background: #f7f8f9;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-header {
    flex-grow: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .merchant-select-container {
      flex-shrink: 0;
      width: 300px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .merchant-select-title {
        margin: 0 10px;
        white-space: nowrap;
      }
    }
  }
  .right-header {
    flex-shrink: 0;
  }

  .el-select :deep(.el-input input) {
    width: 300px;
    height: 36px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 20px;
  }

  > span {
    font-size: 12px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
    line-height: 17px;
    margin-left: 20px;
  }

  .el-dropdown {
    // float: right;
    cursor: pointer;

    .el-dropdown-link img {
      width: 10px;
      height: 6px;
      margin-left: 8px;
      vertical-align: middle;
    }

    > span {
      font-size: 14px;
      font-family: Helvetica;
      text-align: left;
      color: #333333;
      line-height: 17px;
    }

    .el-dropdown-link {
      .account-span {
        display: inline-block;
        max-width: 90px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: middle;
      }
      &.code-container {
        & > img {
          width: 16px;
          height: 16px;
          vertical-align: middle;
          margin-right: 6px;
        }
        & > span {
          color: #333333;
          vertical-align: middle;
          &.is-show-code {
            color: #00b955;
          }
        }
      }
    }
  }

  & > div {
    display: flex;
    align-items: center;
    .package-info {
      margin-left: 10px;
      .infos-container {
        display: flex;
        align-items: center;
        width: 100%;
        overflow: auto;
        .info {
          width: fit-content;
          max-width: 300px;
          margin-right: 10px;
          .infos-day {
            white-space: nowrap;
            flex-wrap: nowrap;
            display: flex;
            // align-items: center;
            .package-name {
              display: inline-block;
              max-width: 130px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }
          & > div {
            height: 20px;
            line-height: 20px;
            &.valid-date {
              & > span:first-child {
                color: #999;
              }
            }
          }
        }
        .btns {
          display: flex;
          flex-wrap: nowrap;
          // @media (max-width: 1366px) {
          //   flex-direction: column;
          // }
          & > span {
            display: inline-block;
            // color: #efefef;
            width: 90px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            cursor: pointer;
            &:hover {
              color: #fff;
              background-color: #efefef;
              border-radius: 2px;
            }
          }
        }
        &.no-package {
          .btns {
            flex-direction: row;
          }
        }
      }
    }
  }
}

.el-dropdown-menu {
  img {
    vertical-align: middle;
    width: 16px;
    height: 16px;
  }
}

.account-dropdown-menu {
  ::v-deep {
    .el-dropdown-menu__item {
      padding-left: 26px;
      padding-right: 26px;
    }
  }
}

.el-dropdown-menu__item.header-code-menu {
  &:hover {
    background-color: #fff !important;
  }
  padding: 0 10px;
  .header-qr-code {
    .imgs {
      display: flex;
      .code-item {
        width: 268px;
        margin-right: 10px;

        .code-img-container {
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .code-btns {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
      & > div {
        color: #333333;
        width: 130px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border-radius: 5px;
        cursor: pointer;
        &.more-code {
          border: 1px solid #eeeeee;
          margin-right: 8px;
        }
        &.all-down {
          color: #ffffff;
          background: #00b955;
          border-radius: 4px;
        }
      }
    }
    .img-container {
      width: 270px;
      & > img {
        width: 100%;
        height: auto;
      }
    }
    .btn-down {
      color: #ffffff;
      height: 36px;
      line-height: 36px;
      text-align: center;
      cursor: pointer;
      margin-top: 10px;
      background-color: #00b955;
      border-radius: 4px;
    }
  }
}
</style>
