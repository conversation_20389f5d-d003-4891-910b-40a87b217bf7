package com.xyy.saas.inquiry.enums.signature;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * 签章平台
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/18 11:38
 */
@Getter
@RequiredArgsConstructor
public enum SignaturePlatformEnum implements IntArrayValuable {

    SELF(0, "自营"),

    FDD(1, "法大大"),
    ;

    private final Integer code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SignaturePlatformEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }


    public static SignaturePlatformEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getCode(), code))
            .findFirst()
            .orElse(SELF);
    }


}
