package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserClockInLogDO;
import jakarta.validation.Valid;

/**
 * 门店员工打卡记录 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantUserClockInLogService {

    /**
     * 创建门店员工打卡记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantUserClockInLog(@Valid TenantUserClockInLogSaveReqVO createReqVO);

    /**
     * 更新门店员工打卡记录
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantUserClockInLog(@Valid TenantUserClockInLogSaveReqVO updateReqVO);

    /**
     * 删除门店员工打卡记录
     *
     * @param id 编号
     */
    void deleteTenantUserClockInLog(Long id);

    /**
     * 获得门店员工打卡记录
     *
     * @param id 编号
     * @return 门店员工打卡记录
     */
    TenantUserClockInLogDO getTenantUserClockInLog(Long id);

    /**
     * 获得门店员工打卡记录分页
     *
     * @param pageReqVO 分页查询
     * @return 门店员工打卡记录分页
     */
    PageResult<TenantUserClockInLogDO> getTenantUserClockInLogPage(TenantUserClockInLogPageReqVO pageReqVO);

}