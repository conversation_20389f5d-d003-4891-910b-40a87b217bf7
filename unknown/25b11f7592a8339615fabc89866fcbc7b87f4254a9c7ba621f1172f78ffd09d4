package com.xyy.saas.inquiry.pojo.prescription;

import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 参与方item
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/04 14:06
 */
@Data
@Schema(description = "参与方item")
@Valid
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ParticipantItem implements Serializable {

    @Schema(description = "参与方uerId")
    private Long userId;

    @Schema(description = "参与方姓名")
    private String name;

    @Schema(description = "参与方手机")
    private String mobile;
    /**
     * 对应  com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField.field 作为 合同 参与方标识 (用于定位当前级 和 查找下一级) {@link PrescriptionTemplateFieldEnum}
     */
    @Schema(description = "参与方标识")
    private String actorField;
    /**
     * 对应  com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField.fieldName 作为 模板 关键字(用于法大大找签名定位)
     */
    @Schema(description = "参与方标识名称")
    private String actorFieldName;

    /**
     * 是否同步三方签章平台
     */
    private Integer accessPlatform;
    /**
     * 签章图片url
     */
    @Schema(description = "参与方签章图片url")
    private String signImgUrl;

    /**
     * 电子签章图片url
     */
    @Schema(description = "参与方电子签章图片url")
    private String signElectronicImgUrl;

    /**
     * 当前顺序
     */
    @Schema(description = "参与方顺序")
    private Integer sort;

    /**
     * 业务id 对应的审核操作记录id
     */
    private Long bizId;

    /**
     * 签署状态
     */
    private Integer signStatus;


}
