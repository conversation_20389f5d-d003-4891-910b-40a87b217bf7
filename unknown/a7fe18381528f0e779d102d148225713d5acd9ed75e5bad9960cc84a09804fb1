package com.xyy.saas.inquiry.signature.server.controller.admin.platform;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignaturePlatformDO;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 签章平台配置")
@RestController
@RequestMapping("/signature/inquiry-signature-platform")
@Validated
public class InquirySignaturePlatformController {

    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    @PostMapping("/create")
    @Operation(summary = "创建签章平台配置")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:create')")
    public CommonResult<Long> createInquirySignaturePlatform(@Valid @RequestBody InquirySignaturePlatformSaveReqVO createReqVO) {
        return success(inquirySignaturePlatformService.createInquirySignaturePlatform(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新签章平台配置")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:update')")
    public CommonResult<Boolean> updateInquirySignaturePlatform(@Valid @RequestBody InquirySignaturePlatformSaveReqVO updateReqVO) {
        inquirySignaturePlatformService.updateInquirySignaturePlatform(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-master")
    @Operation(summary = "更新签章平台为 Master")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:update')")
    public CommonResult<Boolean> updatePlatformMaster(@RequestParam("id") Integer id) {
        inquirySignaturePlatformService.updatePlatformMaster(id);
        return success(true);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除签章平台配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:delete')")
    public CommonResult<Boolean> deleteInquirySignaturePlatform(@RequestParam("id") Long id) {
        inquirySignaturePlatformService.deleteInquirySignaturePlatform(id);
        return success(true);
    }

    @GetMapping("/get")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:query')")
    public CommonResult<InquirySignaturePlatformRespVO> getInquirySignaturePlatform(@RequestParam("id") Long id) {
        InquirySignaturePlatformDO inquirySignaturePlatform = inquirySignaturePlatformService.getInquirySignaturePlatform(id);
        return success(BeanUtils.toBean(inquirySignaturePlatform, InquirySignaturePlatformRespVO.class));
    }

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:query')")
    public CommonResult<PageResult<InquirySignaturePlatformRespVO>> getInquirySignaturePlatformPage(@Valid InquirySignaturePlatformPageReqVO pageReqVO) {
        PageResult<InquirySignaturePlatformDO> pageResult = inquirySignaturePlatformService.getInquirySignaturePlatformPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquirySignaturePlatformRespVO.class));
    }

    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-platform:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquirySignaturePlatformExcel(@Valid InquirySignaturePlatformPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquirySignaturePlatformDO> list = inquirySignaturePlatformService.getInquirySignaturePlatformPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "签章平台配置.xls", "数据", InquirySignaturePlatformRespVO.class,
            BeanUtils.toBean(list, InquirySignaturePlatformRespVO.class));
    }

}