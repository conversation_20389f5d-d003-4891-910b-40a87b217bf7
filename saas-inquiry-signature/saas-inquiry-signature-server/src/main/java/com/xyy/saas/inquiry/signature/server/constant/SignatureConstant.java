package com.xyy.saas.inquiry.signature.server.constant;

/**
 * @Author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:2025/03/27 14:10
 */
public class SignatureConstant {

    /**
     * 签章平台异常时,是否允许自绘处方 - 默认允许
     */
    public static final String CAN_SELF_DRAWN_PRESCRIPTION = "can.self.drawn.prescription";

    /**
     * 处方签章确认mq消费次数，一次10s
     */
    public static final String PRESCRIPTION_SIGNATURE_REQUIRE_COUNT = "prescription.signature.require.count";

}
