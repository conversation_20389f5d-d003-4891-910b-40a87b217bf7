<template>
  <div>
    <Dialog
      v-model="dialogVisible"
      title="首营审核"
      :canFullscreen="false"
      maxHeight="480px"
      width="80%"
    >
      <div>
        <el-radio-group v-model="activeName" style="margin-bottom: 30px">
          <el-radio-button value="1">基本信息</el-radio-button>
          <el-radio-button value="2">资质信息</el-radio-button>
          <el-radio-button value="3">销售人员信息</el-radio-button>
          <el-radio-button value="4">审批记录</el-radio-button>
        </el-radio-group>
      </div>
      <div class="dialog-line"></div>
      <div style="display: flex; justify-content: space-between">
        <div class="tFont">
          <span class="greenLink"></span>
          基本信息
        </div>
        <div
          class="action-from-expand ml-12px cursor-pointer select-none flex justify-start items-center"
          @click="isSHow = !isSHow"
        >
          <span>{{ isSHow ? '收起' : '展开' }}</span>
          <el-icon size="14"><component :is="isSHow ? ArrowUp : ArrowDown" /></el-icon>
        </div>
      </div>
      <el-form :model="formData" disabled label-width="98px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="供应商名称" prop="name">
              <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商类别" prop="name">
              <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商编码" prop="idCard">
              <el-input v-model="formData.idCard" placeholder="" maxlength="18" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="isSHow">
          <el-row>
            <el-col :span="8">
              <el-form-item label="注册日期" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="注册地址" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="法定代表人" prop="idCard">
                <el-input v-model="formData.idCard" placeholder="" maxlength="18" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="委托人" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="委托人身份证" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="委托人身份证有效期" prop="idCard">
                <el-input v-model="formData.idCard" placeholder="" maxlength="18" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="委托书编号" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="委托书有效期" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户银行" prop="idCard">
                <el-input v-model="formData.idCard" placeholder="" maxlength="18" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="银行账号" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户名" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="仓库地址" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="企业负责人" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="质量负责人" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="邮箱" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮政编码" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div class="tFont">
        <span class="greenLink"></span>
        资质信息
      </div>
      <el-table v-loading="loading" :data="list" class="table-center">
        <el-table-column type="index" label="序号" width="60px" />
        <el-table-column label="证书类型" prop="memberPrice" min-width="128" align="center" />
        <el-table-column label="证书编码" prop="businessScope" min-width="108" align="center" />
        <el-table-column label="经营范围" prop="presCategory" min-width="96" align="center" />
        <el-table-column label="发证日期" prop="creator" min-width="96" align="center" />
        <el-table-column label="有效期至" prop="creator" min-width="96" align="center" />
        <el-table-column label="上传图片" prop="creator" min-width="96" align="center" />
      </el-table>
      <div style="display: flex; justify-content: space-between">
        <div class="tFont">
          <span class="greenLink"></span>
          销售人员信息
        </div>
        <div
          class="action-from-expand ml-12px cursor-pointer select-none flex justify-start items-center"
          @click="isSHow1 = !isSHow1"
        >
          <span>{{ isSHow1 ? '收起' : '展开' }}</span>
          <el-icon size="14"><component :is="isSHow1 ? ArrowUp : ArrowDown" /></el-icon>
        </div>
      </div>
      <el-form :model="formData" disabled label-width="98px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="销售员姓名" prop="name">
              <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="name">
              <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证有效期" prop="idCard">
              <el-input v-model="formData.idCard" placeholder="" maxlength="18" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="isSHow1">
          <el-row>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="授权区域" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="授权书号" prop="idCard">
                <el-input v-model="formData.idCard" placeholder="" maxlength="18" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="授权书有效期" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证图片" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="授权书图片" prop="name">
                <el-input v-model="formData.name" placeholder="" maxlength="20" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="授权品种" prop="name">
                
                  <el-checkbox
                    v-model="checkAll"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                  >
                    Check all
                  </el-checkbox>
                  <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                    <el-checkbox v-for="city in cities" :key="city" :label="city" :value="city">
                      {{ city }}
                    </el-checkbox>
                  </el-checkbox-group>
              
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div style="display: flex; justify-content: space-between">
        <div class="tFont">
          <span class="greenLink"></span>
          审批记录
        </div>
        <div
          class="action-from-expand ml-12px cursor-pointer select-none flex justify-start items-center"
          @click="isSHow2 = !isSHow2"
        >
          <span>{{ isSHow2 ? '收起' : '展开' }}</span>
          <el-icon size="14"><component :is="isSHow2 ? ArrowUp : ArrowDown" /></el-icon>
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">返回</el-button>
        <el-button v-if="apprType == 0" type="primary" @click="getChangClick">审批</el-button>
      </template>
    </Dialog>
    <Approval ref="approvalRef" />
  </div>
</template>

<script lang="ts" setup>
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import Approval from './approval.vue'

const props = defineProps({
  id: String,
  auditType: Number
})
const loading = ref(false)
const list = ref([])

const activeName = ref('1')
const apprType = ref()
const isSHow = ref(false)
const isSHow1 = ref(false)
const isSHow2 = ref(false)
const dialogVisible = ref(false)
const approvalRef = ref()
const formData = reactive({
  name: '',
  idCard: '',
  sex: '',
  age: '',
  images: ''
})
const checkAll = ref(false)
const isIndeterminate = ref(true)
const checkedCities = ref(['Shanghai', 'Beijing'])
const cities = ['Shanghai', 'Beijing', 'Guangzhou', 'Shenzhen']

const handleCheckAllChange = (val) => {
  checkedCities.value = val ? cities : []
  isIndeterminate.value = false
}
const handleCheckedCitiesChange = (value) => {
  const checkedCount = value.length
  checkAll.value = checkedCount === cities.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < cities.length
}

const openModal = () => {
  dialogVisible.value = true
}
const getChangClick = () => {
  approvalRef.value.openModal()
}
onMounted(() => {
  apprType.value = props.auditType
})
defineExpose({ openModal })
</script>
<style lang="scss" scoped>
.dialog-line {
  width: 100%;
  height: 1px;
  background-color: #dcdfe6;
}
.tFont {
  width: 132px;
  height: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
  line-height: 18px;
  margin: 10px 0 10px 0;
}
.greenLink {
  display: inline-block;
  width: 4px;
  line-height: 20px;
  height: 18px;
  margin-right: 5px;
  background-color: rgb(6, 175, 0);
}
.text-b {
  font-size: 14px;
  color: #222222;
}
.text-c {
  font-size: 12px;
  line-height: 14px;
}
.text-p {
  font-size: 12px;
  height: 15px;
}
</style>
