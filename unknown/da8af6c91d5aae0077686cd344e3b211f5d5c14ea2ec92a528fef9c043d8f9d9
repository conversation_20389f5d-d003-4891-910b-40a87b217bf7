package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * 药师类型
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PharmacistTypeEnum implements IntArrayValuable {

    DRUGSTORE(1, "药店药师"),

    PLATFORM(2, "平台药师"),

    HOSPITAL(3, "医院药师"),

    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PharmacistTypeEnum::getCode).toArray();


    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static PharmacistTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid PharmacistTypeEnum code: " + code));
    }

    /**
     * 转换审核人类型 eg: 药店药师 - DRUGSTORE 平台药师 - PLATFORM 医院药师 - HOSPITAL
     *
     * @param auditorType 审核人类型 {@link AuditorTypeEnum}
     * @return
     */
    public static PharmacistTypeEnum convertAuditorType(Integer auditorType) {
        if (Objects.equals(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode(), auditorType)) {
            return DRUGSTORE;
        }
        if (Objects.equals(AuditorTypeEnum.PLATFORM_PHARMACIST.getCode(), auditorType)) {
            return PLATFORM;
        }
        if (Objects.equals(AuditorTypeEnum.HOSPITAL_PHARMACIST.getCode(), auditorType)) {
            return HOSPITAL;
        }
        return null;
    }

}