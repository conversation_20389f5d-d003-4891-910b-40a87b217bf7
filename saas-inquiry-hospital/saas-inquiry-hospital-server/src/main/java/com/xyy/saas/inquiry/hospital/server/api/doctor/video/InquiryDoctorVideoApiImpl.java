package com.xyy.saas.inquiry.hospital.server.api.doctor.video;

import com.xyy.saas.inquiry.hospital.api.doctor.video.InquiryDoctorVideoApi;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorVideoService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @Date: 2024/12/10 18:37
 * @Description: 医生录屏api实现层
 */
// @Service
@DubboService
public class InquiryDoctorVideoApiImpl implements InquiryDoctorVideoApi {

    @Resource
    private InquiryDoctorVideoService inquiryDoctorVideoService;

    /**
     * 根据pref查询医生录屏视频
     *
     * @param pref 录屏编码
     * @return 医生录屏视频url地址
     */
    @Override
    public String getVideoUrlByPref(String pref) {
        return inquiryDoctorVideoService.selectVideoUrlByPref(pref);
    }
}
