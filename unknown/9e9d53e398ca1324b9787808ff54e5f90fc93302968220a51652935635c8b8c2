package com.xyy.saas.inquiry.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/28 15:01
 */
@Data
@Schema(description = "商品基础Dto")
@Valid
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProductBaseDto implements Serializable {

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String pref;

    /**
     * 药品类型 {@link com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum}
     */
    @Schema(description = "药品类型 0西药 1-中药")
    private Integer productType;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品通用名称
     */
    @Schema(description = "商品通用名称")
    private String commonName;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private BigDecimal quantity;

    /**
     * 规格
     */
    @Schema(description = "规格")
    private String attributeSpecification;

    /**
     * 生产厂家
     */
    @Schema(description = "生产厂家")
    private String manufacturer;

    /**
     * 批准文号
     */
    @Schema(description = "批准文号")
    private String approvalNumber;

    /**
     * 单位名称 (药品包装单位)
     */
    @Schema(description = "单位名称 (药品包装单位)")
    private String unitName;

    /**
     * 条形码
     */
    @Schema(description = "69码")
    private String barCode;

    @Schema(description = "是否处方药")
    private Integer prescriptionYn;

    @Schema(description = "标准库id")
    private String standardId;

    @Schema(description = "剂型")
    private String dosageForm;
}
