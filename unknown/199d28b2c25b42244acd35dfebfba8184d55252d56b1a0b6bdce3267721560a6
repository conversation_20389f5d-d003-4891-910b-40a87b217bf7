package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/28 17:59
 * @Description: 预问诊审核出参
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ThirdPartyPreInquiryAuditRespVO {

    @Schema(description = "审核成功数量")
    private Integer successNum;

    @Schema(description = "审核失败数量")
    private Integer failNum;

    @Schema(description = "审核失败列表")
    private List<String> failList;
}
