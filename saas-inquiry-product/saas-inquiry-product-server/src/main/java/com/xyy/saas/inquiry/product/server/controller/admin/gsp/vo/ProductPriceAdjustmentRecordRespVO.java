package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 售价调整单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductPriceAdjustmentRecordRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1608")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据编号")
    private String pref;

    @Schema(description = "适用门店")
    private List<Long> applicableTenantIdList;

    @Schema(description = "适用门店", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("适用门店")
    private String applicableTenantIds;

    @Schema(description = "调价原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    @ExcelProperty("调价原因")
    private String adjustmentReason;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("审批状态")
    private Integer approvalStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}