package com.xyy.saas.inquiry.signature.server.service.signature;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractAddParticipantVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import jakarta.validation.Valid;

/**
 * 签章合同 Service 接口
 *
 * <AUTHOR>
 */
public interface InquirySignatureContractService {


    /**
     * 查询签章合同
     *
     * @param reqVO
     * @return
     */
    InquirySignatureContractDO querySignatureContract(InquirySignatureContractSaveReqVO reqVO);

    /**
     * 除非明确知道调用平台是fdd,事后同步的话可能查询不到，建议用上面的  bizId+thirdId查询
     *
     * @param thirdId
     * @param signaturePlatformEnum
     * @return
     */
    InquirySignatureContractDO querySignatureContractByThirdId(String thirdId, SignaturePlatformEnum signaturePlatformEnum);

    InquirySignatureContractDO querySignatureContractByBizId(String bizId, ContractTypeEnum contractTypeEnum);

    /**
     * 保存或获取签章合同 根据 bizId + contractType + signaturePlatform + thirdId
     *
     * @param createReqVO
     * @return
     */
    InquirySignatureContractDO saveOrGetSignatureContractByCondition(InquirySignatureContractSaveReqVO createReqVO);

    /**
     * 修改签章合同状态
     *
     * @return
     */
    boolean updateSignatureContractStatus(@Valid InquirySignatureContractStatusVO signatureContractStatusVO);

    /**
     * 追加合同参与方
     *
     * @param addParticipantVO
     * @return
     */
    InquirySignatureContractDO appendParticipantItem(InquirySignatureContractAddParticipantVO addParticipantVO);


    /**
     * 创建签章合同
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSignatureContract(@Valid InquirySignatureContractSaveReqVO createReqVO);

    /**
     * 更新签章合同
     *
     * @param updateReqVO 更新信息
     */
    void updateSignatureContract(@Valid InquirySignatureContractSaveReqVO updateReqVO);

    /**
     * 删除签章合同
     *
     * @param id 编号
     */
    void deleteSignatureContract(Long id);


    InquirySignatureContractDO validateSignatureContractExists(Long id);

    InquirySignatureContractDO validateSignatureContractPrefExists(String pref);

    /**
     * 获得签章合同
     *
     * @param id 编号
     * @return 签章合同
     */
    InquirySignatureContractDO getSignatureContract(Long id);

    InquirySignatureContractDO getSignatureContractByPref(String pref);

    /**
     * 获得签章合同分页
     *
     * @param pageReqVO 分页查询
     * @return 签章合同分页
     */
    PageResult<InquirySignatureContractDO> getSignatureContractPage(InquirySignatureContractPageReqVO pageReqVO);


}