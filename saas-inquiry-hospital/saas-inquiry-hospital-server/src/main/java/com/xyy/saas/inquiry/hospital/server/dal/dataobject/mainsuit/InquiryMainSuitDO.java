package com.xyy.saas.inquiry.hospital.server.dal.dataobject.mainsuit;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 主诉信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_main_suit")
@KeySequence("saas_inquiry_main_suit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryMainSuitDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 主诉名称
     */
    private String mainSuitName;
    /**
     * 状态 0启用 1禁用
     */
    private Integer status;
    /**
     * 性别限制：0无限制,1限男,2限女
     */
    private Integer sexLimit;

}