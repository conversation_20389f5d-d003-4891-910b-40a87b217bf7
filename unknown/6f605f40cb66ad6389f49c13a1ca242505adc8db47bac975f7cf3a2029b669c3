package com.xyy.saas.inquiry.product.server.controller.admin.product;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentSaveReqVO;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "PC后台 - 商品提报")
@RestController
@RequestMapping("/product/present")
@Validated
@Slf4j
public class ProductPresentController {

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ProductTransferRecordService transferRecordService;

    // 根据条形码查询自建标准库商品
    @GetMapping("/self-by-barcode")
    @Operation(summary = "根据条形码查询自建标准库商品")
    @PreAuthorize("@ss.hasPermission('saas:product:present:query')")
    @Parameter(name = "barcode", description = "条形码", required = true, example = "1024")
    public CommonResult<List<ProductStdlibDto>> getSelfProductInfoByBarcode(@RequestParam String barcode) {
        StdlibProductSearchDto searchDto = new StdlibProductSearchDto()
            .setBarcode(barcode);
        return success(stdlibService.searchStdlibProductList(searchDto, 1));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品提报分页")
    @PreAuthorize("@ss.hasPermission('saas:product:present:query')")
    public CommonResult<PageResult<ProductPresentRecordRespVO>> getPresentPage(@Valid @ParameterObject ProductPresentPageReqVO pageVO) {
        return success(transferRecordService.getLatestPresentPage(pageVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品提报详情")
    @PreAuthorize("@ss.hasPermission('saas:product:present:query')")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<ProductPresentRecordRespVO> getPresentDetail(@RequestParam("id") Long id) {
        return success(transferRecordService.getLatestPresentById(id));
    }
}