package com.xyy.saas.inquiry.hospital.server.service.prescription.external;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.SAAS_PRESCRIPTION_EXTERNAL_NOT_EXISTS;

import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission.PrescriptionExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.external.PrescriptionExternalConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external.SaasPrescriptionExternalDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.external.SaasPrescriptionExternalMapper;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionSupervisionEndProducer;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionDetailService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionSupervisionConditionTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 外配(电子)处方记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SaasPrescriptionExternalServiceImpl implements SaasPrescriptionExternalService {

    @Resource
    private SaasPrescriptionExternalMapper saasPrescriptionExternalMapper;

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private InquiryPrescriptionDetailService inquiryPrescriptionDetailService;

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private TransmissionApi transmissionApi;

    /**
     * 上传监管完成 - 回更就诊登记状态 - 暂无
     */
    @Resource
    private PrescriptionSupervisionEndProducer prescriptionSupervisionEndProducer;

    @Override
    public SaasPrescriptionExternalDO saveSaasPrescriptionExternal(SaasPrescriptionExternalSaveReqVO createReqVO) {
        SaasPrescriptionExternalDO externalDO = saasPrescriptionExternalMapper.selectOneByCondition(SaasPrescriptionExternalPageReqVO.builder().bizId(createReqVO.getBizId()).bizType(createReqVO.getBizType()).build());
        SaasPrescriptionExternalDO saasPrescriptionExternal = PrescriptionExternalConvert.INSTANCE.convert(createReqVO);
        if (externalDO == null) {
            saasPrescriptionExternal.setPref(PrefUtil.getExternalPref());
            saasPrescriptionExternalMapper.insert(saasPrescriptionExternal);
        } else {
            saasPrescriptionExternal.setId(externalDO.getId());
            saasPrescriptionExternalMapper.updateById(saasPrescriptionExternal);
        }
        return saasPrescriptionExternal;
    }

    private void validateSaasPrescriptionExternalExists(Long id) {
        if (saasPrescriptionExternalMapper.selectById(id) == null) {
            throw exception(SAAS_PRESCRIPTION_EXTERNAL_NOT_EXISTS);
        }
    }

    @Override
    public SaasPrescriptionExternalDO getSaasPrescriptionExternal(Long id) {
        return saasPrescriptionExternalMapper.selectById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void externalSupervision(String prescriptionPref) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(InquiryPrescriptionQueryDTO.builder().pref(prescriptionPref).build());
        if (prescriptionRespVO == null) {
            return;
        }
        // 1.过滤基础判断条件
        PrescriptionSupervisionConditionTransmitterDTO dto = PrescriptionExternalConvert.INSTANCE.convertSupervisionCondition(prescriptionRespVO);
        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(prescriptionRespVO.getTenantId()).nodeType(NodeTypeEnum.INTERNET_SUPERVISION_CONDITION).build();
        TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, dto);

        CommonResult<Boolean> baseLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
        log.info("电子处方监管-基础条件判断:prescriptionPref:{},:baseLogic:{}", prescriptionPref, baseLogic);
        if (baseLogic.isError() || !BooleanUtil.isTrue(baseLogic.getData())) {
            return;
        }

        // 2. 处方挂号
        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(prescriptionRespVO.getInquiryPref());
        PrescriptionTransmitterDTO transmitterDTO = PrescriptionExternalConvert.INSTANCE.convertPrescriptionTransmission(prescriptionRespVO, inquiryRecordDetail);

        configReqDTO.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_REGISTRATION_FEEDBACK);
        TransmissionReqDTO trd = TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO);
        
        CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(trd);
        log.info("start-电子处方监管-就诊挂号初始节点:prescriptionPref:{},:businessLogic:{}", prescriptionPref, businessLogic);
        if (businessLogic.isSuccess() && BooleanUtil.isTrue(businessLogic.getData())) {
            CommonResult<PrescriptionExternalTransmissionRespDto> commonResult = transmissionApi.contractInvoke(trd, PrescriptionExternalTransmissionRespDto.class);
            log.info("end-电子处方监管-就诊挂号初始节点:prescriptionPref:{},:commonResult:{}", prescriptionPref, commonResult);

            // if (commonResult.isError()) {
            //     throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), commonResult.getMsg());
            // }
        }

    }


    /**
     * 单独上传处方点评，结算，派药
     *
     * @param prescriptionPref
     */
    private void supervision1(String prescriptionPref) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(InquiryPrescriptionQueryDTO.builder().pref(prescriptionPref).build());
        if (prescriptionRespVO == null) {
            return;
        }
        SaasPrescriptionExternalDO externalDO = saasPrescriptionExternalMapper.selectOneByCondition(SaasPrescriptionExternalPageReqVO.builder().bizId(prescriptionPref).bizType(BizTypeEnum.HYWZ.getCode()).build());
        PrescriptionTransmitterDTO pt = PrescriptionExternalConvert.INSTANCE.convertTransmission(prescriptionRespVO, externalDO);

        // 1. 互联网监管-线上处方点评 - 获取电子处方平台流水号
        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(prescriptionRespVO.getTenantId()).nodeType(NodeTypeEnum.INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_COMMENT).build();
        TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, pt);

        CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
        log.info("电子处方监管-线上处方点评:prescriptionPref:{},:businessLogic:{}", prescriptionPref, businessLogic);
        if (businessLogic.isSuccess() && businessLogic.getData()) {
            CommonResult<PrescriptionExternalTransmissionRespDto> onlinePrescriptionCommentRes = transmissionApi.contractInvoke(transmissionReqDTO,
                PrescriptionExternalTransmissionRespDto.class);
            if (onlinePrescriptionCommentRes.isError()) {
                throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), onlinePrescriptionCommentRes.getMsg());
            }
            pt.setElectronicRxSn(onlinePrescriptionCommentRes.getData().getElectronicRxSn());
            // 保存外配处方数据
            saveSaasPrescriptionExternal(PrescriptionExternalConvert.INSTANCE.convert(prescriptionRespVO, onlinePrescriptionCommentRes.getData()));
        }

        // 2. 互联网监管-诊疗结算
        configReqDTO.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_TREATMENT_SETTLEMENT);
        businessLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
        log.info("电子处方监管-诊疗结算:prescriptionPref:{},:businessLogic:{}", prescriptionPref, businessLogic);
        if (businessLogic.isSuccess() && businessLogic.getData()) {
            // 3. 结算完成后执行下游节点 派药
            // TransmissionReqDTO reqDTO = TransmissionReqDTO.buildReq(TransmissionConfigReqDTO.builder().tenantId(prescriptionRespVO.getTenantId()).nodeType(NodeTypeEnum.INTERNET_SUPERVISION_SEND_DRUG).build(), pt);
            // transmissionReqDTO.setDownstreamReqList(Collections.singletonList(reqDTO));

            CommonResult<PrescriptionExternalTransmissionRespDto> treatmentSettlementRes = transmissionApi.contractInvoke(transmissionReqDTO, PrescriptionExternalTransmissionRespDto.class);
            if (treatmentSettlementRes.isError()) {
                throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), treatmentSettlementRes.getMsg());
            }
        }
    }
}