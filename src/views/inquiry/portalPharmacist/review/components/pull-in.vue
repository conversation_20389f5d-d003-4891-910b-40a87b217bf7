<template>
  <div ref="pullBox" class="pull-box" :class="!modelValue?'pull-box':'pull-out'"  @click="setIsPullIn">
    <div class="pull" :class="!modelValue?'pull':'pull-out'" :title="!modelValue?'收起':'收回'">
      <div class="top"></div>
      <div class="bottom"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emits=defineEmits(['update:modelValue','change'])
interface Props {
  modelValue?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: false
});
function setIsPullIn(){
  emits('update:modelValue', !props.modelValue)
  emits('change', !props.modelValue)
}
const pullBox=ref(null)
</script>

<style scoped>
.pull-box{
  cursor: pointer;
  width: 20px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center
}
.pull{
  display: flex;
  flex-direction: column;
  margin-left: 5px;
  width: 5px;
}
.pull .top{
  position: relative;
  top: 2px;
}
.pull .bottom{
  position: relative;
  bottom: 2px;
}
.pull .top,.pull .bottom{
  width: 4px;
  border-radius: 2px;
  height: 28px;
  transition: .1s ease-in-out;
  background-color:#67c23a;
}
.pull-box:hover .top,.pull:hover .top{
  transform: rotate(12deg) scale(1.15) translateY(-2px);

}
.pull-box:hover .bottom,.pull:hover .bottom{
  transform: rotate(-12deg) scale(1.15) translateY(2px);

}
.pull-out:hover .top{
  transform: rotate(-12deg) scale(1.15) translateY(-2px);
}
.pull-out:hover .bottom{
  transform: rotate(12deg) scale(1.15) translateY(2px);
}
</style>
