package cn.iocoder.yudao.module.system.mq.producer.tenant;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 门店信息变更
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = TenantInfoUpdateEvent.TOPIC
)
public class TenantInfoUpdateProducer extends EventBusRocketMQTemplate {


}
