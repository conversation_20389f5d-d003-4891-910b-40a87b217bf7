package com.xyy.saas.inquiry.enums.prescription.external;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 外配处方类型
 */
@Getter
@RequiredArgsConstructor
public enum ExternalPrescriptionTypeEnum implements IntArrayValuable {


    ELECTRONIC(0, "电子处方"),

    PAPER(1, "纸质处方"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ExternalPrescriptionTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static ExternalPrescriptionTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(ELECTRONIC);
    }
}
