package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorBillingDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorBillingMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_BILLING_NOT_EXISTS;


/**
 * 医生收款信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorBillingServiceImpl implements DoctorBillingService {

    @Resource
    private DoctorBillingMapper doctorBillingMapper;

    @Override
    public Long createDoctorBilling(DoctorBillingSaveReqVO createReqVO) {
        // 插入
        DoctorBillingDO doctorBilling = BeanUtils.toBean(createReqVO, DoctorBillingDO.class);
        doctorBillingMapper.insert(doctorBilling);
        // 返回
        return doctorBilling.getId();
    }

    @Override
    public void updateDoctorBilling(DoctorBillingSaveReqVO updateReqVO) {
        // 校验存在
        validateDoctorBillingExists(updateReqVO.getId());
        // 更新
        DoctorBillingDO updateObj = BeanUtils.toBean(updateReqVO, DoctorBillingDO.class);
        doctorBillingMapper.updateById(updateObj);
    }

    @Override
    public void deleteDoctorBilling(Long id) {
        // 校验存在
        validateDoctorBillingExists(id);
        // 删除
        doctorBillingMapper.deleteById(id);
    }

    private void validateDoctorBillingExists(Long id) {
        if (doctorBillingMapper.selectById(id) == null) {
            throw exception(DOCTOR_BILLING_NOT_EXISTS);
        }
    }

    @Override
    public DoctorBillingDO getDoctorBilling(Long id) {
        return doctorBillingMapper.selectById(id);
    }

    @Override
    public PageResult<DoctorBillingDO> getDoctorBillingPage(DoctorBillingPageReqVO pageReqVO) {
        return doctorBillingMapper.selectPage(pageReqVO);
    }

}