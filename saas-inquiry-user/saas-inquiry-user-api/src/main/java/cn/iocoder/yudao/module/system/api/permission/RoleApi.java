package cn.iocoder.yudao.module.system.api.permission;

import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;

import java.util.Collection;
import java.util.List;

/**
 * 角色 API 接口
 *
 * <AUTHOR>
 */
public interface RoleApi {

    /**
     * 校验角色们是否有效。如下情况，视为无效： 1. 角色编号不存在 2. 角色被禁用
     *
     * @param ids 角色编号数组
     */
    void validRoleList(Collection<Long> ids);

    /**
     * 查询角色列表
     *
     * @param roleIdList 角色ids
     * @return 角色list
     */
    List<RoleRespDTO> selectRoleListByIds(List<Long> roleIdList);


    /**
     * 获取系统角色id
     *
     * @param codes 角色Code
     * @return 角色ids
     */
    List<RoleRespDTO> getSystemRoleIdByCodes(List<String> codes);


}
