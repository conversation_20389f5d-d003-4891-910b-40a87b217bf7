<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigPackageMapper">

  <select id="selectTransmissionConfigPage" resultType="com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO">
    SELECT
    p.*
    FROM saas_transmission_config_package p
    <where>
      p.deleted = 0
      <if test="reqVO.id != null">
        AND p.id = #{reqVO.id}
      </if>
      <if test="reqVO.organType != null">
        AND p.organ_type = #{reqVO.organType}
      </if>
      <if test="reqVO.providerName != null and reqVO.providerName != ''">
        AND p.provider_name LIKE CONCAT('%', #{reqVO.providerName}, '%')
      </if>
      <if test="reqVO.parentPackageId != null">
        AND p.parent_package_id = #{reqVO.parentPackageId}
      </if>
      <if test="reqVO.name != null and reqVO.name != ''">
        AND p.name LIKE CONCAT('%', #{reqVO.name}, '%')
      </if>
      <if test="reqVO.version != null">
        AND p.version = #{reqVO.version}
      </if>
      <if test="reqVO.description != null and reqVO.description != ''">
        AND p.description LIKE CONCAT('%', #{reqVO.description}, '%')
      </if>
      <if test="reqVO.disable != null">
        AND p.disable = #{reqVO.disable}
      </if>
      <if test="reqVO.createTime != null and reqVO.createTime.length == 2">
        AND p.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
      </if>
      <if test="reqVO.apiCode != null and reqVO.apiCode != ''">
        AND exists ( select 1 from saas_transmission_config_item i where p.id = i.config_package_id AND i.deleted = 0 and i.api_code LIKE CONCAT('%', #{reqVO.apiCode}, '%') )
      </if>
      <if test="reqVO.itemDescription != null and reqVO.itemDescription != ''">
        AND exists ( select 1 from saas_transmission_config_item i where p.id = i.config_package_id AND i.deleted = 0 and i.description LIKE CONCAT('%', #{reqVO.itemDescription}, '%') )
      </if>
    </where>
    ORDER BY p.id DESC
  </select>

  <select id="selectByNameAndOrganTypeAndVersion" resultType="com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO">
    select *
    from saas_transmission_config_package
    where name = #{name}
      and organ_type = #{organType}
      and version = #{version} limit 1
  </select>

</mapper> 