import { CONF<PERSON>, ERROR, wsUri } from '@/utils/finger.config';
// 只有在config配置中的方法，才会被调用
// import { Loading,Message } from 'element-ui';
import { ElLoading as Loading, ElMessage as Message } from 'element-plus';
let CONFIG_KEYS = [];
Object.keys(CONFIG).forEach(v => {
  CONFIG_KEYS.push(v);
});
class FingerPrint {
    constructor() {
        this.websocket = null; // websocket实例
        this.type = '';
        this.status = false; // 实例成功状态 false-不成功，true -成功
        this.data = null;
        this.socketLoading = null;
        // this._initWebsocket();
    }
    /**
      * @description: 初始化socket
      * @return: void
      */
    // _initWebsocket() {
      initWebsocket(callback){
        const _this = this;
        _this.callback = callback;
        this.socketLoading = Loading.service({
          lock:true,
          text:'指纹机连接中...',
          spinner:'el-icon-loading',
          background:'rgba(0,0,0,.7)'
        });
        setTimeout(() => {
          if(!this.status){
            this.socketLoading.close()
            Message({
              message:'指纹机连接超时，请刷新重试',
              type:'error',
            })
          }
        }, 5000);
        _this.websocket = new WebSocket(wsUri); 
        // 打开socket
        _this.websocket.onopen = function(evt) {
            _this._openDevice();
        };
        // socket关闭
        _this.websocket.onclose = function(evt) { 
            _this.status = false;
            _this.socketLoading.close()
            Message({
              message:'设备连接失败，请刷新后重试',
              type:'error'
            })

        };
        // socket异常
        _this.websocket.onerror = function(evt) { 
            _this.status = false;
            _this.socketLoading.close()
        };
        // socket 接收信息
        _this.websocket.onmessage = function(evt) { 
          const data = JSON.parse(evt.data);
          const result = Object.assign(data, {
              msg: ERROR[data.ret]
          })
          const type = result.function;
          if(result.ret!==0&&result.msg&&!_this.callback){
              Message({
                message:result.msg,
                type:'error'
              })
          }
          // 接收消息回调方法, 只有在config配置中的方法，才会被调用
          if(CONFIG_KEYS.indexOf(type) !== -1) {
            CONFIG[type](_this, result);
          }
      };
    }
    /**
      * @description: 获取设备状态,所有外调方法，都会先走此方法，判断打卡机状态正常后，再走正常业务流程
      * @param: {Object} 传参对象
      * @return: void
      */
    onOperateFinger(data) {
        this.data = data;
        const { template, callback, type } = this.data
        this.callback = callback;
        this.template = template;
        this.type = type;
        let str = "{\"module\":\"fingerprint\",\"function\":\"getstatus\",\"parameter\":\"\"}";
        this._doSend(str);
    }
    /**
      * @description: 指纹操作命令，调此方法后，再按打卡机，会进行指纹对比，返回对比结果根据ret 状态码判断
      * ret: 0;       比对成功
      * ret: 不为0;   比对失败
      * @return: void
      */
    onFingerOption() {
        // 取消指纹对比
        if (this.type !== 'compare') {
            this.type = '';
            this.callback = null;
        }
    }
    /**
      * @description: 添加指纹，执行此命令后，再按打卡机，会进行3次指纹添加
      * @return: void
      */
    onAddFinger() {
        let str = "{\"module\":\"fingerprint\",\"function\":\"register\",\"parameter\":\"\"}";
        this._doSend(str);
    }
    /**
      * @description: 取消添加指纹操作，发送此指令，下一次按打卡机，按正常的打卡机交互流程走，此后会一直走finger.config.js中的oncapture方法
      * @return: void
      */
    onCancelAddFinger() {
        let str = "{\"module\":\"fingerprint\",\"function\":\"cancelregister\",\"parameter\":\"\"}";
        this._doSend(str);
    }
    closeDevice() {
      this._doSend("{\"module\":\"fingerprint\",\"function\":\"close\",\"parameter\":\"\"}");
    }
    /**
      * @description: 打开设备
      * @return: void
      */
    _openDevice() {
        let str = "{\"module\":\"fingerprint\",\"function\":\"open\",\"parameter\":{\"FakeFunOn\":0}}"
        this._doSend(str)
    }
    /**
      * @description: 发送信息
      * @return: void
      */
    _doSend(message) {
        this.websocket?.send(message);
    } 
    /**
      * @description: 业务消息完成后回调
      * @return: void
      */
    _messageCallback(data) {
        this.callback && this.callback(data);
    }
    /**
      * @description: 通过，驳回等业务，需要指纹对比
      * @return: void
      */
     _onFingerCompare() {
       
        const { template, template2 } = this;
        let str = "{\"module\":\"fingerprint\",\"function\":\"verify\",\"parameter\":{" + "\"template1\":\"" + template  + "\",\"template2\":\"" + template2+"\"}}";
        this._doSend(str)
    }
    /**
      * @description: 清除指纹状态
      * @return: void
      */
    _onClearStatus() {
        this.type = "";
    }
}

export default FingerPrint;