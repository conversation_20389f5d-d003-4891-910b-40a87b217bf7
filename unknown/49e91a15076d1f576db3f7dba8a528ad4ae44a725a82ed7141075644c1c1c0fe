package com.xyy.saas.inquiry.signature.server.service.prescription.strategy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;

/**
 * 处方签章Strategy
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/27 15:47
 */
public interface SignaturePrescriptionStrategy {


    SignaturePlatformEnum getPlatform();

    /**
     * 签发处方笺
     *
     * @param psDto
     */
    CommonResult<?> issuePrescription(PrescriptionSignatureInitDto psDto);

    /**
     * 审核处方
     *
     * @param psAuditDto
     * @return
     */
    CommonResult<?> auditPrescription(PrescriptionSignatureAuditDto psAuditDto);

}
