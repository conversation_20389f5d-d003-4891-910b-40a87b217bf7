package com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 处方外配 TransmissionDto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescriptionExternalTransmissionRespDto extends BaseDO {


    /**
     * 三方系统处方号
     */
    private String externalRxPref;


    /**
     * 电子处方平台流水号
     */
    private String electronicRxSn;


}