package com.xyy.saas.transmitter.server.controller.admin.config;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackagePageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageRespVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import com.xyy.saas.transmitter.server.service.config.TransmissionConfigPackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;


@Tag(name = "管理后台 - 协议配置包")
@RestController
@RequestMapping("/transmitter/transmission-config-package")
@Validated
public class TransmissionConfigPackageController {

    @Resource
    private TransmissionConfigPackageService transmissionConfigPackageService;

    @PostMapping("/create")
    @Idempotent
    @Operation(summary = "创建协议配置包")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:create')")
    public CommonResult<Integer> createTransmissionConfigPackage(@Valid @RequestBody TransmissionConfigPackageSaveReqVO createReqVO) {
        return success(transmissionConfigPackageService.createTransmissionConfigPackage(createReqVO));
    }

    @PutMapping("/update")
    @Idempotent
    @Operation(summary = "更新协议配置包")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:update')")
    public CommonResult<Boolean> updateTransmissionConfigPackage(@Valid @RequestBody TransmissionConfigPackageSaveReqVO updateReqVO) {
        transmissionConfigPackageService.updateTransmissionConfigPackage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除协议配置包")
    @Idempotent
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:delete')")
    public CommonResult<Boolean> deleteTransmissionConfigPackage(@RequestParam("id") Integer id) {
        transmissionConfigPackageService.deleteTransmissionConfigPackage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得协议配置包")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:query')")
    public CommonResult<TransmissionConfigPackageRespVO> getTransmissionConfigPackage(@RequestParam("id") Integer id) {
        TransmissionConfigPackageDO transmissionConfigPackage = transmissionConfigPackageService.getTransmissionConfigPackage(id);
        return success(BeanUtils.toBean(transmissionConfigPackage, TransmissionConfigPackageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得协议配置包分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:query')")
    public CommonResult<PageResult<TransmissionConfigPackageRespVO>> getTransmissionConfigPackagePage(@Valid TransmissionConfigPackagePageReqVO pageReqVO) {
        PageResult<TransmissionConfigPackageRespVO> pageResult = transmissionConfigPackageService.getTransmissionConfigPackagePage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出协议配置包 Excel")
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransmissionConfigPackageExcel(@Valid TransmissionConfigPackagePageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransmissionConfigPackageRespVO> list = transmissionConfigPackageService.getTransmissionConfigPackagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "协议配置包.xls", "数据", TransmissionConfigPackageRespVO.class,
            list);
    }

    @PostMapping("/copy")
    @Operation(summary = "复制协议配置包")
    @Parameter(name = "id", description = "原配置包编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-config-package:create')")
    @Idempotent
    public CommonResult<Integer> copyTransmissionConfigPackage(@RequestParam("id") Integer id) {
        return success(transmissionConfigPackageService.copyTransmissionConfigPackage(id));
    }

}