package cn.iocoder.yudao.module.system.util;

import cn.iocoder.yudao.module.system.BaseIntegrationTest;
import org.jose4j.jwt.JwtClaims;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Import({JwtUtil.class, Jose4jTokenProperties.class})
class JwtUtilTest extends BaseIntegrationTest {

    @Test
    void builder_and_verify() throws Exception {
        String sub = "2:1", tk = "token";
        String jwt = JwtUtil.builder(sub, tk);
        assertNotNull(jwt);
        JwtClaims jwtClaims = JwtUtil.verify(jwt);
        assertNotNull(jwtClaims);
        assertEquals(jwtClaims.getSubject(), sub);
        assertEquals(jwtClaims.getClaimValueAsString("token"), tk);
    }
}