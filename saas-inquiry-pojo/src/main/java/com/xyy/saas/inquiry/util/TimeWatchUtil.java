package com.xyy.saas.inquiry.util;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2024/10/11 14:32
 */
@Slf4j
public class TimeWatchUtil {

    public static <T> T excute(Supplier<T> supplier, String methodName) {
        long l = System.currentTimeMillis();
        T t = supplier.get();
        log.info("检测调用方法耗时,methodName:{},耗时:{}ms", methodName, System.currentTimeMillis() - l);
        return t;
    }

    public static void excute(Runnable runnable, String methodName) {
        long l = System.currentTimeMillis();
        runnable.run();
        log.info("检测调用方法耗时,methodName:{},耗时:{}ms", methodName, System.currentTimeMillis() - l);
    }
}
