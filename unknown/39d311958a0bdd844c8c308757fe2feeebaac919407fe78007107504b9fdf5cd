package com.xyy.saas.inquiry.im.server.mq.message.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2024/12/11 15:09
 * @Description: 转码任务开始消息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TranscodingTaskMessage implements Serializable {

    /**
     * 问诊单号
     */
    private String InquiryPref;

    /**
     * 转码任务id
     */
    private String taskId;
}
