<template>
  <Dialog v-model="dialogVisible" title="操作确认" width="550" :fullscreen="false" align-center >
    <template #default>
      <div class="flex justify-center content-center content">
        <el-icon class="icon"><QuestionFilled /></el-icon>
        <div class="flex-col">
          <div class="text1" v-if="selectType == 'check'"
            >是否对选中的{{ idList.length }}条数据，进行批量审核操作？</div
          >
          <div class="text1" v-if="selectType == 'radio'">是否对本条数据，进行审核操作？</div>
          <div class="text2">审批后无法撤销操作</div>
          <el-form :rules="rules" :model="formData" >
            <el-form-item label="审核结果:" prop="result"  class="mt2"> 
              <el-select v-model="formData.result" placeholder="请输入审核结果" >
                <el-option v-for="item in selectList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="cancelClick"> 确定 </el-button>
      </div>
    </template>
  </Dialog>
<!-- 
  <Dialog v-model="dialog" title="操作确认" width="550" :fullscreen="false" align-center>
    <template #default>
      <div class="flex justify-center content-center content">
        <el-icon class="icon"><QuestionFilled /></el-icon>
        <div class="flex-col">
          <div class="text1" v-if="selectType == 'check'"
            >是否确定进行批量{{ doType == 'detele' ? '删除' : '审核' }}操作？</div
          >
          <div class="text1" v-if="selectType == 'radio'"
            >是否确定进行{{ doType == 'detele' ? '删除' : '审核' }}操作？</div
          >
          <div class="text2" v-if="doType == 'review'">审批后无法撤销操作</div>
          <div class="text2" v-if="doType == 'detele'">删除后该数据将无法恢复</div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog = false">取消</el-button>
        <el-button type="primary" @click="cancelClick"> 确定 </el-button>
      </div>
    </template>
  </Dialog> -->
</template>

<script setup>
import { QuestionFilled } from '@element-plus/icons-vue'
import { PenddingReviewApi } from '@/api/inquiry/inquirydata/pendingReview'

defineOptions({ name: 'InquiryPendingReviewDialog' })
const message = useMessage()
const emit = defineEmits(['success'])
const dialogVisible = ref(false)
const dialog = ref(false)
const idList = ref([])
const selectType = ref('')
const doType = ref('')
const open = (type, list, doWhat) => {
  doType.value = doWhat
  selectType.value = type
  idList.value = list
  formData.value.result = 1
  dialogVisible.value = true
}
const formData = ref({
  result: 1
})
const rules = ref({
  result: [
    { required: true, message: '请输入审核结果', trigger: 'blur' },
  ]
})
const selectList = [
  {
    label: '审核通过',
    value: 1
  },
  {
    label: '审核驳回',
    value: 2
  }
]
defineExpose({ open })
const cancelClick = async (a) => {
  try{
    let data
    if(selectType.value == 'check') {
      data = await PenddingReviewApi.preInquiryAudit({ prefList: idList.value,auditStatus: formData.value.result})
 
    } else {
      data = await PenddingReviewApi.preInquiryAudit({ pref: idList.value[0] , auditStatus: formData.value.result})
    }
    dialogVisible.value = false
    dialog.value = false
     emit('success',{
        type: doType.value,
        list: idList.value,
        status:formData.value.result,
        failList: data?.failList,
        failNum: data?.failNum,
        successNum: data?.successNum
      })
    message.success('操作成功')
  }catch(err){
    console.log(err)
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 150px;
  padding: 30px 0;
  .icon {
    margin-right: 10px;
    font-size: 30px;
    color: #ff9900;
  }
  .text1 {
    font-size: 16px;
    font-weight: 600;
  }
  .text2 {
    color: #aaaaaa;
    margin-top: 10px;
  }
}
</style>
