package com.xyy.saas.inquiry.pojo.condition;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/11/15 18:06
 */
@Data
public class ConditionGroup implements Serializable {

    /**
     * 条件是否并列, 默认为or
     */
    private boolean and;
    /**
     * 多条件
     */
    private List<Condition> conditions;
    /**
     * 序号
     */
    private int seq;
}
