<template>
    <el-tabs v-model="activeName">
      <el-tab-pane label="首营商品" name="1">
        <first-purchase-items ref="basicInfoRef"  />
      </el-tab-pane>

      <el-tab-pane label="首营供应商" name="2">
        <first-time-supplier ref="InquiryServerRef" />
      </el-tab-pane>
    </el-tabs>
</template>
<script lang="ts" setup>
import {firstPurchaseItems} from "@/views/gspStandard/model";
import {firstTimeSupplier} from "@/views/gspStandard/model";

import {useRouter,useRoute } from "vue-router"
import {onMounted,ref} from 'vue'
const route = useRoute();
defineOptions({ name: 'FirstPurchaseApproval' })
onMounted(() => {
})


const activeName = ref('1') // Tag 激活的窗口



</script>
