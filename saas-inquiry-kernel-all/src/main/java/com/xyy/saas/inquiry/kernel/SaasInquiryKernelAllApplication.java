package com.xyy.saas.inquiry.kernel;


import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableDubbo(scanBasePackages = {"com.xyy.saas.inquiry"})
// @EnableDiscoveryClient
//@EnableEventBus
@SpringBootApplication(scanBasePackages = {"com.xyy.common", "cn.iocoder.yudao.module", "com.xyy.saas.inquiry"})
@Slf4j
public class SaasInquiryKernelAllApplication {

    public static void main(String[] args) {
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "inquiry-kernel-all");
        // 设置文件名编码格式，-Dsun.jnu.encoding=UTF-8 设置无效
        System.setProperty("sun.jnu.encoding", "UTF-8");
        SpringApplication.run(SaasInquiryKernelAllApplication.class, args);
        log.info("sun.jnu.encoding:{}", System.getProperty("sun.jnu.encoding"));
    }

}
