package cn.iocoder.yudao.module.system.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationStatusChangeDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.*;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageCostConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageRelationConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageRelationMapper;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostMessageDto;
import cn.iocoder.yudao.module.system.mq.producer.tenant.TenantPackageCostProducer;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageChangeEvent;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostSaveEvent;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.*;

/**
 * 门店套餐订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantPackageRelationServiceImpl implements TenantPackageRelationService {

    private static final Logger log = LoggerFactory.getLogger(TenantPackageRelationServiceImpl.class);
    @Resource
    private TenantPackageRelationMapper tenantPackageRelationMapper;

    @Resource
    private TenantPackageService tenantPackageService;

    @Resource
    private TenantService tenantService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private TenantPackageCostProducer tenantPackageCostProducer;

    @Resource
    @Lazy
    private AdminUserService adminUserService;

    /**
     * 开通门店套餐包 {@link com.xyy.saas.inquiry.drugstore.server.mq.consumer.cost.DrugStorePackageCostConsumer}
     *
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_TENANT_PACKAGE_RELATION, subType = SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUB_TYPE, bizNo = "{{#createReqVO.tenantId}}",
        success = SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUCCESS)
    public Long createTenantPackageRelation(TenantPackageRelationSaveReqVO createReqVO) {
        // 校验门店基本信息
        tenantService.validTenant(createReqVO.getTenantId());

        TenantPackageRelationDO tenantPackageRelation = TenantPackageRelationConvert.INSTANCE.tenantPackageRelationInitReqVO2DO(createReqVO);

        // 非自定义套餐
        if (!TenantPackageConstant.isRechargePackageId(createReqVO.getPackageId())) {
            // 套餐信息查询
            TenantPackageDO tenantPackageDO = tenantPackageService.validTenantPackage(createReqVO.getPackageId());
            // 构建套餐对象并计填充部分数据(服务时间和状态等)
            TenantPackageRelationConvert.INSTANCE.fillPackageRelationInfo(tenantPackageDO, tenantPackageRelation);
        }

        // 生成套餐订单插入
        tenantPackageRelationMapper.insert(tenantPackageRelation);
        // mq记录套餐额度 + 问诊套餐额度变更记录
        recordPackageCostMq(tenantPackageRelation);
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("tenantPackageRelation", tenantPackageRelation);
        // 返回
        return tenantPackageRelation.getId();
    }

    @Override
    @LogRecord(type = SYSTEM_TENANT_PACKAGE_RELATION, subType = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.tenantId}}",
        success = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUCCESS)
    public void updateTenantPackageRelation(TenantPackageRelationSaveReqVO updateReqVO) {
        // 校验存在
        TenantPackageRelationDO oldDo = validateTenantPackageRelationExists(updateReqVO.getId());
        // 更新
        TenantPackageRelationDO updateObj = TenantPackageRelationConvert.INSTANCE.tenantPackageRelationUpdateReqVO2DO(updateReqVO);
        tenantPackageRelationMapper.updateById(updateObj);
        // mq记录套餐额度 + 问诊套餐额度变更记录
        recordPackageCostMq(updateObj);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldDo, TenantPackageRelationSaveReqVO.class));
        LogRecordContext.putVariable("_oldObj", oldDo);
    }

    /**
     * mq记录套餐额度 + 问诊套餐额度变更记录
     *
     * @param tenantPackageRelation 开通信息
     */
    public void recordPackageCostMq(TenantPackageRelationDO tenantPackageRelation) {
        if (tenantPackageRelation == null || tenantPackageRelation.getStartTime() == null || tenantPackageRelation.getEndTime() == null) {
            return;
        }
        TenantPackageCostMessageDto tenantPackageCostMessageDto = TenantPackageCostConvert.INSTANCE.tenantPackageRelationDO2CostMessage(tenantPackageRelation);
        tenantPackageCostProducer.sendMessage(TenantPackageCostSaveEvent.builder().msg(tenantPackageCostMessageDto).build());
    }

    @Override
    @LogRecord(type = SYSTEM_TENANT_PACKAGE_RELATION, subType = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUB_TYPE, bizNo = "{{#tenantPackageRelation.tenantId}}",
        success = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_STATUS_SUCCESS)
    public void updateTenantPackageRelationStatus(TenantPackageRelationStatusChangeReqVO invalidReqVO) {
        TenantPackageRelationDO tenantPackageRelationDO = validateTenantPackageRelationExists(invalidReqVO.getId());
        // 套餐包状态判断
        if (TenantPackageRelationStatusEnum.finalStatus(tenantPackageRelationDO.getStatus())) {
            throw exception(TENANT_PACKAGE_RELATION_STATUS_ERROR, TenantPackageRelationStatusEnum.fromStatusCode(tenantPackageRelationDO.getStatus()).getDesc());
        }
        TenantPackageRelationDO updateObj = TenantPackageRelationConvert.INSTANCE.invalidConvert(invalidReqVO);
        tenantPackageRelationMapper.updateById(updateObj);
        // 发送变更
        recordPackageCostStatusMq(updateObj.setTenantId(tenantPackageRelationDO.getTenantId()));
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("_oldObj", tenantPackageRelationDO);
        LogRecordContext.putVariable("status", TenantPackageRelationStatusEnum.fromStatusCode(invalidReqVO.getStatus()).getDesc());
        LogRecordContext.putVariable("oriStatus", TenantPackageRelationStatusEnum.fromStatusCode(tenantPackageRelationDO.getStatus()).getDesc());
    }


    @Override
    public void batchUpdateTenantPackageRelationStatus(TenantPackageRelationStatusChangeReqVO statusChangeReqVO) {

        List<TenantPackageRelationDO> relationDOS = tenantPackageRelationMapper.selectBatchIds(statusChangeReqVO.getTenantPackageRelationIds());
        if (relationDOS.stream().anyMatch(r -> TenantPackageRelationStatusEnum.finalStatus(r.getStatus()))) {
            throw exception(TENANT_PACKAGE_RELATION_EXISTS_STATUS_ERROR);
        }
        // 批量变更状态
        List<TenantPackageRelationDO> updateStatus = TenantPackageRelationConvert.INSTANCE.convertUpdateStatus(relationDOS, TenantPackageRelationStatusEnum.fromStatusCode(statusChangeReqVO.getStatus()),
            statusChangeReqVO.getStatusChangeInfo());
        if (CollUtil.isEmpty(updateStatus)) {
            return;
        }
        tenantPackageRelationMapper.updateById(updateStatus);
        for (TenantPackageRelationDO relationDO : updateStatus) {
            recordPackageCostStatusMq(relationDO);
        }
    }

    /**
     * 处理发送额度状态mq
     *
     * @param relationDO
     */
    public void recordPackageCostStatusMq(TenantPackageRelationDO relationDO) {
        TenantPackageCostMessageDto tenantPackageCostMessageDto = TenantPackageCostConvert.INSTANCE.convertStatus2CostMsg(relationDO);
        tenantPackageCostMessageDto.setUpdateStatus(true);
        tenantPackageCostMessageDto.setCreator(SecurityFrameworkUtils.getLoginUserId() == null ? "null" : SecurityFrameworkUtils.getLoginUserId().toString());
        tenantPackageCostProducer.sendMessage(TenantPackageCostSaveEvent.builder().msg(tenantPackageCostMessageDto).build());
    }

    /**
     * 切套餐包(作废旧套餐，新增套餐) 1.问诊类型：新套餐比旧套餐少则去掉,多则新增 2.问诊额度：剩余额度和新套餐额度 取最小值 例如: 旧 图文 100  余额 10 新 视频 200  图文 50： 余额 视频200  图文 10 新 视频 200  图文 150： 余额 视频200  图文 60 (|150-100| + 剩余的10 ) 新 图文 5 ：                余额 5
     * <p>
     * 3.有效期： 新旧套餐年限相同,有效期不变; 新套餐有效期>旧套餐,套餐时间：按旧 【开始时间】+新期限 计算有效期; 新套餐有效期<旧套餐,套餐时间：按旧 【当天时间】+新期限 计算有效期; 例如: 今天 1.10 号  旧 : 1.1 - 1.30  有效期 1月 新套餐>旧  2月 ： 1.1 - 2.30  (+2月) 新套餐<旧  1天 :  1.1 - 1.11 (今天+1天)
     * <p>
     * 今天 1.20 号  旧 : 1.1 - 1.10  有效期 10天 新套餐>旧  2月 ： 1.1 - 2.30  (+2月) 新套餐<旧  1天 :  1.1 - 1.21 (今天+1天)
     *
     * @param changeVO 切换VO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateTenantPackageRelation(TenantPackageRelationChangeVO changeVO) {
        // 查询基础套餐和旧包信息
        TenantPackageDO newTc = tenantPackageService.validTenantPackage(changeVO.getPackageId());
        List<TenantPackageRelationDO> tenantPackageRelationDOS = tenantPackageRelationMapper.selectBatchIds(changeVO.getTenantPackageRelationIds());
        // 自定义套餐 || 最终状态的套餐  不处理变更
        tenantPackageRelationDOS = tenantPackageRelationDOS.stream().filter(tcb -> !TenantPackageConstant.isRechargePackageId(tcb.getPackageId())
            && !TenantPackageRelationStatusEnum.finalStatus(tcb.getStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tenantPackageRelationDOS)) {
            return;
        }
        // 旧套餐Map
        Map<Long, TenantPackageDO> oldPackageDOMap = tenantPackageService.getTenantPackageDoByCondition(
                TenantPackageReqDto.builder().packageIds(CollectionUtils.convertList(tenantPackageRelationDOS, TenantPackageRelationDO::getPackageId)).build())
            .stream().collect(Collectors.toMap(TenantPackageDO::getId, Function.identity(), (a, b) -> b));
        // 组装新套餐，计算时间
        List<TenantPackageRelationDO> newTcbList = tenantPackageRelationDOS.stream().filter(t -> oldPackageDOMap.containsKey(t.getPackageId())).map(tcb -> {
            TenantPackageRelationDO newTcb = TenantPackageRelationConvert.INSTANCE.tenantPackageRelationInitCopy(tcb);  // 复制新套餐包
            compareAndCalculatTime(newTc, oldPackageDOMap.get(newTcb.getPackageId()), newTcb);     // 比较计算时间
            return newTcb;
        }).collect(Collectors.toList());
        if (CollUtil.isEmpty(newTcbList)) {
            return;
        }
        // 批量作废 取 newTcbList的旧id
        List<TenantPackageRelationDO> updateStatus = TenantPackageRelationConvert.INSTANCE.convertUpdateStatus(newTcbList, TenantPackageRelationStatusEnum.ABANDONED,
            TenantPackageRelationStatusChangeDto.builder().remark("批量更换套餐,作废旧套餐").build());
        // 批量新增
        newTcbList = newTcbList.stream().peek(n -> {
            n.setOldId(n.getId());
            n.setId(null);
        }).collect(Collectors.toList());
        tenantPackageRelationMapper.insertBatch(newTcbList);
        tenantPackageRelationMapper.updateById(updateStatus);

        List<TenantPackageCostMessageDto> tpcmList = TenantPackageCostConvert.INSTANCE.convertChangeTcbCosts(newTcbList);
        // 1. 作废之前的套餐包 (需一并发送mq作废和新增,不可用updateTenantPackageRelationStatus方法)
        for (TenantPackageCostMessageDto costMessage : tpcmList) {
            tenantPackageCostProducer.sendMessage(TenantPackageChangeEvent.builder().msg(costMessage).build());
        }
    }

    /**
     * 计算有效期： 新旧套餐年限相同,有效期不变; 新套餐有效期>旧套餐,套餐时间：按旧 【开始时间】+新期限 计算有效期; 新套餐有效期<旧套餐,套餐时间：按旧 【结束时间】+新期限 计算有效期; 例如: 今天 1.10 号  旧 : 1.1 - 1.30  有效期 1月 新套餐>旧  2月 ： 1.1 - 2.30  (+2月) 新套餐<旧  1天 :  1.1 - 1.11 (今天+1天)
     * <p>
     * 今天 1.20 号  旧 : 1.1 - 1.10  有效期 10天 新套餐>旧  2月 ： 1.1 - 2.30  (+2月) 新套餐<旧  1天 :  1.1 - 1.21 (今天+1天)
     *
     * @param newTc  新套餐
     * @param oldTc  旧套餐
     * @param newTcb 新套餐包 此时对象的属性还没有替换,经过此方法后得到新数据
     */
    private void compareAndCalculatTime(TenantPackageDO newTc, TenantPackageDO oldTc, TenantPackageRelationDO newTcb) {
        TenantPackageRelationConvert.INSTANCE.fillPackageInfo(newTc, newTcb); // 填充新套餐信息

        if (Objects.equals(newTc.getTerm(), oldTc.getTerm()) && Objects.equals(newTc.getTermType(), oldTc.getTermType())) {
            return; // 新旧套餐年限相同,有效期不变;
        }
        LocalDateTime oldEndTime = newTcb.getEndTime(); // 旧结束时间
        LocalDateTime newEndTime = DateTermTypeEnum.fromCode(newTc.getTermType()).calculationTermDate(newTcb.getStartTime(), newTc.getTerm()); // 新结束时间
        if (oldEndTime.equals(newEndTime)) {
            return;
        }
        if (newEndTime.isAfter(oldEndTime)) { // 新套餐比旧套餐久,取新套餐结束时间
            newTcb.setEndTime(newEndTime);
        }
        if (newEndTime.isBefore(oldEndTime)) { //  新套餐比旧套餐早到期  取当前时间 + 新套餐时限
            newTcb.setEndTime(DateTermTypeEnum.fromCode(newTc.getTermType()).calculationTermDate(newTc.getTerm()));
        }

    }

    @Override
    public void deleteTenantPackageRelation(Long id) {
        // 校验存在
        validateTenantPackageRelationExists(id);
        // 删除
        tenantPackageRelationMapper.deleteById(id);
    }

    private TenantPackageRelationDO validateTenantPackageRelationExists(Long id) {
        TenantPackageRelationDO tenantPackageRelationDO = tenantPackageRelationMapper.selectById(id);
        if (tenantPackageRelationDO == null) {
            throw exception(TENANT_PACKAGE_RELATION_NOT_EXISTS);
        }
        return tenantPackageRelationDO;
    }

    @Override
    public TenantPackageRelationDO getTenantPackageRelation(Long id) {
        return tenantPackageRelationMapper.selectById(id);
    }

    @Override
    public PageResult<TenantPackageRelationRespVO> getTenantPackageRelationPage(TenantPackageRelationPageReqVO pageReqVO) {
        IPage<TenantPackageRelationDO> pageResult = tenantPackageRelationMapper.getTenantPackageRelationPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
            return PageResult.empty();
        }
        return new PageResult<>(getPackageRelationRespVOS(pageResult.getRecords()), pageResult.getTotal());
    }


    @Override
    public List<TenantPackageRelationRespVO> getTenantPackageRelationList(TenantPackageReqDto reqDto) {
        List<TenantPackageRelationDO> relationList = tenantPackageRelationMapper.getTenantPackageRelationList(reqDto);
        return getPackageRelationRespVOS(relationList);
    }

    @Override
    public List<TenantPackageRelationRespVO> getTenantPackageRelations(TenantPackageReqDto reqDto) {
        List<TenantPackageRelationDO> relationList = tenantPackageRelationMapper.getTenantPackageRelationList(reqDto);
        return TenantPackageRelationConvert.INSTANCE.convertDo2Vo(relationList);
    }

    /**
     * 组装其他关联数据  套餐 门店 医院
     *
     * @param list
     * @return
     */
    private List<TenantPackageRelationRespVO> getPackageRelationRespVOS(List<TenantPackageRelationDO> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        list = adminUserService.fillUserInfo(list); // 填充操作人信息
        // 套餐
        Map<Long, TenantPackageDO> packageDOMap = tenantPackageService.getTenantPackageDoByCondition(TenantPackageReqDto.builder().packageIds(CollectionUtils.convertList(list, TenantPackageRelationDO::getPackageId)).build())
            .stream().collect(Collectors.toMap(TenantPackageDO::getId, Function.identity(), (a, b) -> b));
        // 门店
        Map<Long, TenantDO> tenantDOMap = tenantService.getTenantList(CollectionUtils.convertList(list, TenantPackageRelationDO::getTenantId))
            .stream().collect(Collectors.toMap(TenantDO::getId, Function.identity(), (a, b) -> b));
        // 医院
        List<String> inquiryHospitalPrefs = list.stream().filter(tpd -> CollUtil.isNotEmpty(tpd.getHospitalPrefs())).flatMap(tpd -> tpd.getHospitalPrefs().stream()).distinct().collect(Collectors.toList());
        // Map<String, InquiryHospitalRespDto> inquiryHospitalsMap = inquiryHospitalApi.getInquiryHospitalsBaseInfoMap(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(inquiryHospitalPrefs).build());
        return list.stream().map(tp -> {
            TenantPackageRelationRespVO vo = TenantPackageRelationConvert.INSTANCE.convertVO(tp);
            TenantPackageRelationConvert.INSTANCE.fillBaseInfo(vo, packageDOMap.getOrDefault(vo.getPackageId(), new TenantPackageDO()), tenantDOMap.getOrDefault(vo.getTenantId(), new TenantDO()));
            // Optional.ofNullable(vo.getHospitalPrefs()).ifPresent(hids -> {
            //     String hospitalName = hids.stream().distinct().map(hid -> inquiryHospitalsMap.getOrDefault(hid, new InquiryHospitalRespDto()).getName()).collect(Collectors.joining(","));
            //     vo.setHospitalName(hospitalName);
            // });
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    public List<TenantPackageRelationDO> getTenantPackageRelationList(Long tenantId) {
        return tenantPackageRelationMapper.getListByTenantId(tenantId);
    }

    @Override
    public Long getTenantCountByPackageId(Long id) {
        return tenantPackageRelationMapper.selectCount(TenantPackageRelationDO::getPackageId, id);
    }
}