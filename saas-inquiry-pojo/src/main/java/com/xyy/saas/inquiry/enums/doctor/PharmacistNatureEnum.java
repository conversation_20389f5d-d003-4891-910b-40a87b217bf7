package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 药师性质
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PharmacistNatureEnum implements IntArrayValuable {

    NORMAL_PHARMACIST(0, "常规药师"),

    HEAD_PHARMACIST(1, "总部药师"),

    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PharmacistNatureEnum::getCode).toArray();


    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static PharmacistNatureEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid PharmacistTypeEnum code: " + code));
    }
}