package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 医院科室信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryHospitalDepartmentRelationService {

    /**
     * 创建医院科室信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryHospitalDepartmentRelation(@Valid InquiryHospitalDepartmentRelationSaveReqVO createReqVO);


    /**
     * 删除医院科室信息
     *
     * @param id 编号
     */
    void deleteInquiryHospitalDepartmentRelation(Long id);

    /**
     * 获得医院科室信息
     *
     * @param id 编号
     * @return 医院科室信息
     */
    InquiryHospitalDepartmentRelationDO getInquiryHospitalDepartmentRelation(Long id);

    /**
     * 获得医院科室信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医院科室信息分页
     */
    PageResult<InquiryHospitalDepartmentRelationDO> getInquiryHospitalDepartmentRelationPage(InquiryHospitalDepartmentRelationPageReqVO pageReqVO);


    /**
     * 医院科室维护
     *
     * @param createReqVO
     * @return
     */
    Boolean editHospitalDept(InquiryHospitalDepartmentRelationSaveReqVO createReqVO);

    /**
     * 根据互联网医院id查询医院科室信息
     *
     * @param hospitalId
     * @return
     */
    List<Long> getDeptIdByHospitalId(Long hospitalId);

    List<InquiryHospitalDepartmentRelationRespVO> getDeptByHospitalPref(String hospitalPref);
}