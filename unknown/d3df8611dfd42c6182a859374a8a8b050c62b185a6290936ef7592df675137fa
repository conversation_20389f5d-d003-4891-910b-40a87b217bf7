package com.xyy.saas.inquiry.product.server.dal.mysql.product;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品资质信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductQualificationInfoMapper extends BaseMapperX<ProductQualificationInfoDO> {

    /**
     * 根据商品id查询商品资质信息
     * @param productPrefList
     * @return
     */
    default List<ProductQualificationInfoDO> selectList(List<String> productPrefList) {
        if (CollectionUtils.isEmpty(productPrefList)) {
            return List.of();
        }
        return selectList(new LambdaQueryWrapperX<ProductQualificationInfoDO>()
            .in(ProductQualificationInfoDO::getProductPref, productPrefList.stream().distinct().toList())
            .orderByDesc(ProductQualificationInfoDO::getCreateTime));
    }

    default void deleteByProductPref(String productPref) {
        delete(ProductQualificationInfoDO::getProductPref, productPref);
    }


    /**
     * 批量插入或更新
     * @param list
     * @return
     */
    int insertOrUpdateOnDuplicate(List<ProductQualificationInfoDO> list);


    /**
     * 根据商品pref列表物理删除数据
     * @param prefList
     * @return
     */
    int hardDeleteByProductPrefList(List<String> prefList);

}