package com.xyy.saas.inquiry.enums.transmitter;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务包环境枚举
 */
@Getter
@AllArgsConstructor
public enum ServiceEnvEnum {

    TEST(0, "测试"),
    GRAY(1, "灰度"),
    PROD(2, "上线");

    /**
     * 环境编码
     */
    private final Integer code;

    /**
     * 环境描述
     */
    private final String desc;

    /**
     * 获取环境描述
     *
     * @param code 环境编码
     * @return 环境描述
     */
    public static String getDesc(Integer code) {
        if (code == null) {
            return "";
        }
        for (ServiceEnvEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    /**
     * 判断是否为线上环境
     *
     * @param code 环境编码
     * @return 是否为线上环境
     */
    public static boolean isProd(Integer code) {
        return PROD.getCode().equals(code);
    }
} 