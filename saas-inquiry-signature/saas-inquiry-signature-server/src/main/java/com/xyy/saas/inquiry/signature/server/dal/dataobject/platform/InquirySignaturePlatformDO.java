package com.xyy.saas.inquiry.signature.server.dal.dataobject.platform;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 签章平台配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_signature_platform", autoResultMap = true)
@KeySequence("saas_inquiry_signature_platform_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquirySignaturePlatformDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 签章平台 0-自签署 1-法大大
     */
    private Integer signaturePlatform;
    /**
     * 是否为主签章平台
     */
    private Boolean master;
    /**
     * 属性名 eg:私有云地址 对应枚举
     */
    private String paramName;
    /**
     * 属性值 eg:8000809
     */
    private String paramValue;
    /**
     * 描述
     */
    private String description;

    /**
     * 平台应用扩展信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<PlatformConfigExtDto> ext;


}