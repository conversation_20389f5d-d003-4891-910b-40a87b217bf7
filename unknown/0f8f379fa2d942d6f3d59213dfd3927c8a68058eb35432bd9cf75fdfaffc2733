package com.xyy.saas.inquiry.pharmacist.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PharmacistAuditEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 药师延迟审核MQ
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PharmacistAuditEvent.TOPIC
)
public class PharmacistDelayAuditProducer extends EventBusRocketMQTemplate {


}
