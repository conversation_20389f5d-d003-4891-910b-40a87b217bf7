import {
	baseURL,
	getAccessToken
} from "@/sheep/request"
import {
	getTerminal
} from '@/sheep/util/const';
// #ifdef APP-PLUS
import {
	checkPermission,
	requestPermissions
} from '@/uni_modules/permission-handler';
// #endif
import AppHandle from "@/class/AppHandle"
const appHandle = new AppHandle()
var that = null;

export const bridgeEvent = {
	// 选择图片
	chooseImage: async (data) => {
		console.info("data", data)
		return new Promise((resolve, reject) => {
			// #ifdef APP-PLUS
			uni.chooseImage({
				// sourceType: 'album',
				count: data.data.count,
				success: async (res) => {
					const accessToken = getAccessToken();
					// console.log('accessToken', accessToken)
					let Authorization = '';
					let terminal = '';
					let Accept = '*/*';
					if (accessToken) {
						Authorization = 'Bearer ' + accessToken;
					}
					terminal = getTerminal();
					let urls = []
					console.log(res.tempFilePaths.length)
					let total = res.tempFilePaths.length
					that.sendRequestData({ // 向H5发送消息
						result: {
							type: 'showLoadingCount',
							count: total,
							name: data.data.name,
						},
						originData: {
							type: 'sendUniData'
						}
					}, 2)
					for await (let item of res.tempFilePaths) {
						let imageSrc = item
						uni.uploadFile({ //上传图片
							url: baseURL +
								'/system/file/upload', //开发者服务器 url
							filePath: imageSrc, //要上传文件资源的路径。
							fileType: 'image', //文件类型，image/video/audio
							header: {
								"Content-Type": "multipart/form-data",
								Authorization: Authorization,
								Accept,
								terminal
							},
							name: 'file', //文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容
							success: (res) => { //接口调用成功的回调函数
								console.log(
									'uploadImage success, res is:',
									JSON
									.parse(res.data))
								const respData = JSON.parse(res
									.data)
								const url = respData.data
								urls.push(url)
								if (urls.length == total) {
									that.sendRequestData({ // 向H5发送消息
										result: {
											type: 'chooseImage',
											urls: urls,
											name: data.data.name,
										},
										originData: {
											type: 'sendUniData'
										}
									}, 2)
								}
							},
							fail: (err) => { //接口调用失败的回调函数	
								console.log('失败返回：', err);
								that.sendRequestData({ // 向H5发送消息
									result: {
										type: 'showLoadingCount',
										count: 0,
										name: data.data.name,
									},
									originData: {
										type: 'sendUniData'
									}
								}, 2)
								uni.showToast({
									icon: "none",
									title: "上传失败"
								})
							}
						});
					}
					that.sendRequestData({ // 向H5发送消息
						result: {
							type: 'UploadCompleted',
							name: data.data.name,
						},
						originData: {
							type: 'sendUniData'
						}
					}, 2)
				}
			})

			// #endif
			// #ifdef MP-WEIXIN
			// #endif
			// #ifdef H5
			// #endif
		})
	},
	// h5发送消息通知创建im实例
	readyCreatedIm: (data) => {
		console.info("readyCreatedIm", JSON.stringify(data))
		if (data.accessToken) {
			uni.setStorageSync('accessToken', data.accessToken);
		}
		that.initTim()
	},
	// 保存图片
	saveImage: (data) => {
		console.info("保存图片url==============", data.data)
		uni.downloadFile({
			url: data.data,
			success: async (res) => {
				if (res.statusCode === 200) {
					const storageRes = await appHandle.toRequestAuth("storage")
					const photoRes = await appHandle.toRequestAuth("photo")
					if (storageRes != 1 || photoRes != 1) {
						return
					}
					uni.saveImageToPhotosAlbum({
						filePath: res.tempFilePath,
						success: function() {
							uni.showToast({
								title: "保存成功",
								icon: "none"
							});
						},
						fail: function() {
							uni.showToast({
								title: "保存失败，请稍后重试",
								icon: "none"
							});
						}
					});
				}
			}
		})
	},

	// 拨号
	call: async (data) => {
		const toRequestAuthRes = await appHandle.toRequestAuth("callPhone")
		if (toRequestAuthRes != 1) {
			return
		}
		console.info("拨号==============", data.data)
		uni.makePhoneCall({
			phoneNumber: data.data //仅为示例
		});

	},
	// 显示头部 showNav
	showNav: (data) => {
		console.info("显示头部==============", data.data)
		// uni.makePhoneCall({
		// 	phoneNumber: data.data //仅为示例
		// });
		// #ifdef APP-PLUS
		// that.webView.setTitleNViewSearchInputText("sdfsd")
		// plus.navigator.setFullscreen(true);//显示手机顶部状态栏
		// plus.webview.getDisplayWebview()[0].setTitleNViewSearchInputText("text")
		// #endif

	},
	// 隐藏软键盘
	hideKeyboard: () => {
		setTimeout(function() {
			uni.hideKeyboard(); //隐藏软键盘

		}, 250);
	},
	// 接收webview路由变化的监听
	watchRouterH5Change: (data) => {
		console.info(data.data)
		uni.setStorageSync('webviewRouter', JSON.stringify(data.data));
	},
	// 打开子窗体
	openSubPage() {
		// 通过 id 获取 nvue 子窗体  
		const subNVue = uni.getSubNVueById('chat')
		// 打开 nvue 子窗体  
		subNVue.show('slide-in-left', 300, function() {
			// 打开后进行一些操作...  
			//   
		});
	},
	/**
	 * @desc 判断语音,相机权限权限
	 * @param {Object} data
	 */
	checkRecordAndCamera: async () => {
		const recordPermissionRes = await appHandle.recordPermission()
		const cameraAuthorityRes = await appHandle.cameraAuthority()
		console.log("录音权限:", recordPermissionRes)
		console.log("相机权限:", cameraAuthorityRes)
		if (recordPermissionRes && recordPermissionRes) {
			that.sendRequestData({ // 向H5发送消息
				result: 'checkRecordAndCamera',
				originData: {
					type: 'sendUniData'
				}
			}, 2)
		}

	},
	/*
	 *  @desc 原生方法获取权限
	 */
	nativeCheckPermission(type) {
		return new Promise((resolve) => {
			checkPermission(type, (result) => {
				// result=result==''?'authorized':''
				return resolve(result)
			})
		})
	},

	/**
	 * @desc 安卓权限校验
	 */
	androidAuthCheck(type, scene, permissionTip) {

		return new Promise(async (resolve) => {
			try {
				var platform = uni.getSystemInfoSync().platform;
				const sdkVersion = parseInt(plus.os.version.split('.')[0], 10);
				console.log("sdkVersion:", sdkVersion)
				if (platform == 'ios') {
					return resolve(1)
				}
				if (platform == 'android') {
					// 相册
					const photoPermission = await bridgeEvent.nativeCheckPermission('Photos')
					const storagePermission = await bridgeEvent.nativeCheckPermission('Storage')
					console.log("photoPermission", photoPermission)
					console.log("storagePermission", storagePermission)
					// 相机

					const cameraPermission = await bridgeEvent.nativeCheckPermission('Camera')
					console.log("cameraPermission", cameraPermission)
					// 麦克风
					const microphonePermission = await bridgeEvent.nativeCheckPermission('Microphone')
					console.log("microphonePermission", microphonePermission)
					// ============视频问诊
					if (scene === 'videoInterview') {
						if (cameraPermission != "granted" ||
							microphonePermission != "granted") {
							plus.nativeUI.confirm(
								"荷叶问诊APP 需要授予相机和麦克风权限，用于视频问诊", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					}
					// 上传图片统一处理
					if (scene === 'chooseImage') {
						if (cameraPermission != "granted" ||
							(photoPermission != "granted" && storagePermission != "granted")) {
							plus.nativeUI.confirm(
								permissionTip || "荷叶问诊APP 需要授予相机和相册权限", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					}
					// ============图文问诊+聊天选择图片V 扫码用药V+聊天V+表单V
					if (scene === 'GrapSelectPictures') {
						if (cameraPermission != "granted" ||
							(photoPermission != "granted" && storagePermission != "granted")) {
							plus.nativeUI.confirm(
								"荷叶问诊APP 需要授予相机和相册权限，用于图文问诊", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					}
					// ============举报上传图片V
					if (scene === 'ReportUploadImage') {
						if (cameraPermission != "granted" ||
							(photoPermission != "granted" && storagePermission != "granted")) {
							plus.nativeUI.confirm(
								"荷叶问诊APP 需要授予相机和相册权限，用于举报", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					}
					// ============商品提报上传图片V
					if (scene === 'ProductSubmissionUploadImage') {
						if (cameraPermission != "granted" ||
							(photoPermission != "granted" && storagePermission != "granted")) {
							plus.nativeUI.confirm(
								"荷叶问诊APP 需要授予相机和相册权限，用于商品提报", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					}
					// ============聊天访问麦克风V
					if (scene === 'ChatMicrophone') {
						const appAuthorizeSetting = uni.getAppAuthorizeSetting()
						console.log("appAuthorizeSetting", appAuthorizeSetting.microphoneAuthorized)
						if (appAuthorizeSetting.microphoneAuthorized != "authorized") {
							plus.nativeUI.confirm(
								"荷叶问诊APP 需要授予麦克风权限，用于图文问诊聊天", // 消息内容
								function(e) {
									// 用户点击按钮后的回调函数
									// e.index 表示用户点击的按钮索引，从1开始
									if (e.index === 1) {
										return resolve(1)
									} else if (e.index === 2) {
										return resolve(0)
									}
								},
								'提示', // 标题
								["暂不开启", "马上设置"] // 按钮数组，数组中的字符串表示按钮上的文字
							);
						} else {
							return resolve(1)
						}
					}
				}

			} catch (e) {
				return resolve(1)
			}



		})


	},
	/**@desc 统一授权函数
	 * @param {Object} request
	 * type 授权类型 camera|record|photo|storage|callPhone || [camera,record,photo]
	 * scene 授权场景: 
	 * 	 videoInterview=视频问诊
	 *   chooseImage=选择图片
	 *   GrapSelectPictures = 图文问诊+聊天选择图片 扫码用药+聊天+表单
	 *   ChatMicrophone=聊天访问麦克风
	 *   ReportUploadImage=举报上传图片
	 */
	appRequestAuth: async (request) => {
		try {
			console.log("request.data", request.data)
			const type = request.data.type
			const scene = request.data.scene
			const permissionTip = request.data?.permissionTip
			const name = request.data?.name
			const androidAuthCheckRes = await bridgeEvent.androidAuthCheck(type, scene, permissionTip)
			if (!androidAuthCheckRes) {
				return
			}
			if (typeof type === 'string') { // 单个授权
				const res = await appHandle.toRequestAuth(type, {})
				console.log("res", res)
				if (res) {
					that.sendRequestData({ // 向H5发送消息
						result: {
							type: 'appRequestAuth',
							scene: request.data.scene,
							name: request.data?.name || ""
						},
						originData: {
							type: 'sendUniData'
						}
					}, 2)
				}

			} else { // 多个授权
				let checkAuths = []
				for await (let s of type) {
					console.log(scene, s)
					let toRequestAuthRes = await appHandle.toRequestAuth(s, {})
					checkAuths.push(toRequestAuthRes)
				}
				if (checkAuths.every(item => Boolean(item))) {
					that.sendRequestData({ // 向H5发送消息
						result: {
							type: 'appRequestAuth',
							scene: request.data.scene,
							name: request.data?.name || ""
						},
						originData: {
							type: 'sendUniData'
						}
					}, 2)
				}
			}
		} catch (e) {
			console.log('eeee', e)
			that.sendRequestData({ // 向H5发送消息
				result: {
					type: 'appRequestAuth',
					scene: request.data.scene,
					name: request.data?.name || ""
				},
				originData: {
					type: 'sendUniData'
				}
			}, 2)
		}

	}
}

export default function bridge(data) {
	if (data && data.type && bridgeEvent.hasOwnProperty(data.type)) {
		that = this;
		bridgeEvent[data.type].apply(this, [data])
	}
}