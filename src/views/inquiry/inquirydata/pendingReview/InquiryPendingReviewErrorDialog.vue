<template>
  <Dialog v-model="dialogVisible" title="错误提示" width="700" :fullscreen="false" align-center :scroll="true" maxHeight="400px">
    <template #default>
      <div class="audit-result-container">
        <div class="content">
          <div class="title">审核结果:</div>

          <div class="summary">
            <div class="success-item">
              <span class="label">审核成功条数:</span>
              <span class="count">{{ successCount }}条</span>
            </div>
            <div class="fail-item">
              <span class="label">审核失败条数:</span>
              <span class="count">{{ failCount }}条</span>
            </div>
          </div>
        </div>
        <el-alert  type="info" :closable="false"> 
          <template #title>
            <span style="color: #222222">错误提示:</span>
          </template>
          <ul class="error-list">
            <li v-for="(error, index) in errorMessages" :key="index" class="error-item">
              {{ error }}
            </li>
          </ul>
        </el-alert>
        <!-- <div class="error-section" v-if="failCount > 0">
          <h3>错误提示:</h3>
       
        </div> -->
      </div>
    </template>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import { ref } from 'vue'

// 模拟数据 - 实际使用时可以从props或API获取
const successCount = ref(8)
const failCount = ref(2)
const errorMessages = ref([
  '第1行：【问诊流水号】审核失败，失败原因：xxxxx',
  '第2行：【问诊流水号】审核失败，失败原因：xxxxx'
])

const handleClose = () => {
  // 关闭逻辑，例如返回上一页或关闭弹窗
  console.log('关闭审核结果')
  // 实际使用时可能是: emit('close') 或 router.back()
}
const dialogVisible = ref(false)
const open = (res) => {
  dialogVisible.value = true
  errorMessages.value = res.failList
  failCount.value = res.failNum
  successCount.value = res.successNum
}
defineExpose({ open })
</script>

<style scoped>
.audit-result-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  display: flex;
  flex-direction: column;

  .content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
}

.title {
  font-weight: bold;
  color: #222222;
  font-size: 18px;
  margin-bottom: 8px;
}

.summary {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-bottom: 30px;
}

.success-item,
.fail-item {
  display: flex;
  align-items: center;
}

.count {
  font-size: 16px;
  font-weight: bold;
}

.success-item .count {
  color: #4caf50;
}

.fail-item .count {
  color: #f44336;
}

.label {
 font-size: 16px;
  color: #222222;
}

.error-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff6f6;
  border-radius: 4px;
  border-left: 4px solid #f44336;
}

h3 {
  color: #f44336;
  margin-bottom: 10px;
}

.error-list {
  list-style-type: none;
  padding-left: 0;
}

.error-item {
  padding: 8px 0;
  color: #666;
  border-bottom: 1px solid #eee;
}

.error-item:last-child {
  border-bottom: none;
}

.close-btn {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 30px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.close-btn:hover {
  background-color: #0b7dda;
}
</style>
