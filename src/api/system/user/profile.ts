import request from '@/config/axios'
import { number } from 'vue-types'

export interface ProfileVO {
  id: number
  username: string
  nickname: string
  dept: {
    id: number
    name: string
  }
  roles: {
    id: number
    name: string
  }[]
  posts: {
    id: number
    name: string
  }[]
  socialUsers: {
    type: number
    openid: string
  }[]
  email: string
  mobile: string
  sex: number
  avatar: string
  status: number
  remark: string
  loginIp: string
  loginDate: Date
  createTime: Date
}

export interface UserProfileUpdateReqVO {
  nickname: string
  email: string
  mobile: string
  sex: number
}

// 查询用户个人信息
export const getUserProfile = () => {
  return request.get({ url: '/system/user/profile/get' })
}

// 修改用户个人信息
export const updateUserProfile = (data: UserProfileUpdateReqVO) => {
  return request.put({ url: '/system/user/profile/update', data })
}

// 用户密码重置
export const updateUserPassword = (oldPassword: string, newPassword: string) => {
  return request.put({
    url: '/system/user/profile/update-password',
    data: {
      oldPassword: oldPassword,
      newPassword: newPassword
    }
  })
}

// 用户头像上传
export const uploadAvatar = (data) => {
  return request.upload({ url: '/system/user/profile/update-avatar', data: data })
}

//手机号是否已经绑定
export const statusMobileBinding = (phone: number) => {
  return request.get({})
}

//请求发送验证码
export const verificationCode = (mobile: string, scene: number) => {
  return request.post({
    url: '/system/auth/send-sms-code',
    data: {
      mobile: mobile,
      scene: scene
    },
    ignoreError: true
  })
}
//发送手机验证码v1
export const v1Code = (mobile: string, scene: number) => {
  return request.post({
    url: '/system/auth/send-sms-code-v1',
    data: {
      mobile: mobile,
      scene: scene
    },
    ignoreError: true
  })
}
//绑定门店数量
export const numberBoundStores = (phone: number) => {
  return request.get({})
}

//换绑手机号
export const phoneNumbeBinding = (params: {
  newMobile: boolean;
  scene: number;
  mobile: string;
  code: number;
  confirm?: boolean;
}) => {
  return request.putOriginal({
    url: 'system/user/profile/change-mobile',
    data: params,
    ignoreError: true
  })
}
