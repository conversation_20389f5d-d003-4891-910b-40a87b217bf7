package com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalSettingDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 医院参数配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryHospitalSettingMapper extends BaseMapperX<InquiryHospitalSettingDO> {

    default List<InquiryHospitalSettingDO> selectByHospitalId(Long hospitalId) {
        return selectByHospitalIds(List.of(hospitalId));
    }

    default List<InquiryHospitalSettingDO> selectByHospitalIds(List<Long> hospitalIds) {
        if (CollectionUtils.isEmpty(hospitalIds)) {
            return List.of();
        }
        return selectList(new LambdaQueryWrapperX<InquiryHospitalSettingDO>()
            .in(InquiryHospitalSettingDO::getHospitalId, hospitalIds)
            .orderByDesc(InquiryHospitalSettingDO::getId));
    }

    /**
     * 批量插入或更新
     *
     * @param list
     * @return
     */
    int insertOrUpdateBatch(List<InquiryHospitalSettingDO> list);

    /**
     * 根据模板id查询关联使用的医院id列表
     *
     * @param presTempIdList
     * @return
     */
    List<Long> selectPresTempUsedHospitalIdList(List<Long> presTempIdList);

}