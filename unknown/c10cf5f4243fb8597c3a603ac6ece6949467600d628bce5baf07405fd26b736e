package cn.iocoder.yudao.module.system.service.system;

import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.controller.app.tenant.vo.SystemDefaultConfigVO;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/29 17:32
 */
@Service
@Validated
public class SystemDefaultServiceImpl implements SystemDefaultService {

    @Resource
    private ConfigApi configApi;

    // 密码登录是否需要验证码
    static final String INQUIRY_PWD_LOGIN_REQUIRE_VERIFY_CODE = "inquiry.pwd.login.require.verify.code";


    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SYSTEM_DEFAULT_CONFIG + "#10m", key = "'defaultConfig'")
    public SystemDefaultConfigVO getAppDefaultConfig() {
        String pwdLoginRequireVerifyCode = configApi.getConfigValueByKey(INQUIRY_PWD_LOGIN_REQUIRE_VERIFY_CODE); // 密码登录是否需要验证码
        SystemDefaultConfigVO configVO = new SystemDefaultConfigVO().setPwdLoginRequireVerifyCode(!StringUtils.isBlank(pwdLoginRequireVerifyCode) && Boolean.parseBoolean(pwdLoginRequireVerifyCode));
        return configVO;
    }
}
