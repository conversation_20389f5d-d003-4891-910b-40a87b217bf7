package com.xyy.saas.inquiry.product.server.config.forward.dto;


import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidPictureProResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 8440893959234058656L;
    private String productCode;
    private String productId;
    List<MidPictureInfoVo> pictureInfoList;
    private String pictureVersion;
    private Integer pictureCount;
    private String createTime;
    private Date createDate;
    private Integer isMaxPictureVersion = 0;


}
