package com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryTenantPharmacistRelationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 门店药师关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryTenantPharmacistRelationMapper extends BaseMapperX<InquiryTenantPharmacistRelationDO> {


    default void deleteByPharmacistId(Long pharmacistId) {
        delete(new LambdaQueryWrapperX<InquiryTenantPharmacistRelationDO>().eq(InquiryTenantPharmacistRelationDO::getPharmacistId, pharmacistId));
    }

    default void deleteByPharmacistIdTenantIds(Long pharmacistId, List<Long> tenantIds) {
        delete(new LambdaQueryWrapperX<InquiryTenantPharmacistRelationDO>().eq(InquiryTenantPharmacistRelationDO::getPharmacistId, pharmacistId).in(InquiryTenantPharmacistRelationDO::getTenantId, tenantIds));
    }
}