package com.xyy.saas.inquiry.im.api.message.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/2/7 19:55
 * @Description: 问诊最后一条消息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryLastMessageDto implements Serializable {

    /**
     * 问诊单号
     */
    private String inquiryPref;

    /**
     * 最后一条消息扩展内容
     */
    private String msgExt;

    /**
     * 最后一条消息
     */
    private String lastMsg;
}
