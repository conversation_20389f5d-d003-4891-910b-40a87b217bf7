<!-- 修改指纹 -->
<template>
  <Dialog  v-model="showDialog" :title="title" width="480px" @close="confirm" :close-on-press-escape="false" :close-on-click-modal="false">
    <div class="dialog">
      <el-steps 
        style="margin: 10px auto 30px;" 
        :active="active" 
        finish-status="success" 
        align-center 
      >
        <el-step title="校验密码" >
          <template #icon>
            <img :src="changeIcon(0)" alt="" class="step_icon"/>
          </template>
        </el-step>
        <el-step title="录入指纹" >
          <template #icon>
            <img :src="changeIcon(1)" alt="" class="step_icon"/>
          </template>
        </el-step>
        <el-step title="修改成功" >
          <template #icon>
            <img :src="changeIcon(2)" alt="" class="step_icon"/>
          </template>
        </el-step>
      </el-steps>
      <el-form
        ref="ruleFormRef"
        style="margin:0 auto;"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        position: relative
        :size="formSize"
      
      > 
        <div >
          <el-form-item v-if="active === STEP_ACTIVE.VALID" label="账号名" prop="userName" >
            <el-input v-model="ruleForm.userName" placeholder="请输入" disabled/>
          </el-form-item>
          <el-form-item v-if="active === STEP_ACTIVE.VALID" label="密码" prop="usePassword" >
            <el-input type="password" v-model="ruleForm.usePassword" placeholder="请输入" clearable/>
          </el-form-item>
        </div>
        <div>
            <div v-if="active===STEP_ACTIVE.FINGER" class="wrap">
              <FingerprintClock type="updata" width="120px"  @success="getFinger" v-if="active===STEP_ACTIVE.FINGER"/>
            </div>
        </div>
        <el-form-item v-if="active === STEP_ACTIVE.NO_VALID || active===STEP_ACTIVE.DONE"  label="" >
          <div class="success_tip">
            <span class="tip">修改指纹成功</span>
            <span>快来试试使用新指纹上下班打卡吧~</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="active === STEP_ACTIVE.DONE" type="primary" 
          style="width: 88px;
          height: 44px;
          background: #00B955;
          border-radius: 4px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #FFFFFF;
          letter-spacing: 0;" 
          @click="confirm" 
        >
          确认
        </el-button>
        <el-button v-if="active === STEP_ACTIVE.VALID" type="primary"  style="background-color:#00B955" @click="next">
          下一步
        </el-button>
      </div>
    </template>
</Dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import FingerprintClock from '@/views/inquiry/portalPharmacist/components/FingerprintClock.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import step_success from '@/assets/inquiry/portalPharmacist/step_success.png'
import step_active from '@/assets/inquiry/portalPharmacist/step_active.png'
import step_noActive from '@/assets/inquiry/portalPharmacist/step_noActive.png'
import { InquiryPortalPharmacistApi } from '@/api/inquiry/portalPharmacist'
const { wsCache } = useCache()
const message = useMessage()
// 分步表单状态枚举值
enum STEP_ACTIVE {
  VALID = 0, // 校验
  FINGER = 1,
  NO_VALID = 2, // 不会到达的状态：修改成功页面的图标始终是完成
  DONE = 3,
}

const active = ref(STEP_ACTIVE.VALID)
const ruleFormRef = ref<FormInstance>()
interface RuleForm {
  userName: string
  usePassword: string
}
const ruleForm = ref<RuleForm>({
  userName:'',
  usePassword:'',
})

const rules = reactive<FormRules<RuleForm>>({
  userName: [
    { min: 1, max: 22, message: '请输入合理的账号名称长度', trigger: 'blur' },
  ],
  usePassword: [
    {
      required: true,
      message: '请填写密码',
      trigger: 'blur' 
    },
  ],
})
const showDialog = ref(false) 
// 定义触发事件的方法
const emit = defineEmits(['success','colse']);


/**
 * 弹窗方法
 */
const title = ref('修改指纹')//弹窗标题
const open = async (data) => {
  title.value = data?.title
  active.value = STEP_ACTIVE.VALID // 重置到第一步
  const userInfo = wsCache.get(CACHE_KEY.USER)
  ruleForm.value.userName = userInfo?.user?.mobile
  ruleForm.value.usePassword = ''
  state.value = {
    userId:userInfo?.user?.id,
    tenantId:userInfo?.tenant?.id,
    fingerprintInfo:'',
    type:data?.type
  }
  showDialog.value = true
}
defineExpose({
  open
})


/**
 * 关闭，确定按钮
 */
//校验反参
const state = ref({
  userId:'',
  tenantId:'',
  fingerprintInfo:'',//指纹信息
  type:''
})
// const close = () => {
//   emit('colse')
//   showDialog.value = false
// } 
const confirm = () => {
  emit('colse')
  showDialog.value = false
}
/**
 * 下一步
 */
const next = () => {
    // 第一步，校验密码
    if(active.value === STEP_ACTIVE.VALID){
      // 获取表单的引用
      ruleFormRef.value?.validate(async (valid) => {
        if (valid) {
          const data = {
            userId: state.value.userId,
            tenantId: state.value.tenantId,
            password: ruleForm.value.usePassword,
            checkPwd:true
          };
          await InquiryPortalPharmacistApi.updateFinger(data);
          active.value++; // 点击下一步时，active 值自增
        } else {
          // 如果表单验证失败，弹出提示消息
        }
      });
    } else if(active.value === STEP_ACTIVE.FINGER){
      // 开始录入指纹的操作

    }
     else if(active.value === STEP_ACTIVE.NO_VALID){
      // 如果修改成功，进入DONE页面
      active.value = STEP_ACTIVE.DONE
      emit('success',state.value)
    }
  };


  //校验指纹成功回调
  const getFinger = (val) => {
    active.value++;
    state.value.fingerprintInfo = val.fingerprintInfo
    handleUpdateFinger();
  }
  /**
   * @description:更新指纹模板
   * @return: void
   */
  const handleUpdateFinger = async() => {
    const data = {
      userId: state.value.userId,
      tenantId: state.value.tenantId,
      fingerPrintInfo:state.value.fingerprintInfo,
      checkPwd:false
    };
    try {
      // TODO: 修改接口
      await InquiryPortalPharmacistApi.updateFinger(data);
      message.success("指纹更新成功");
      next();  // 更新成功，进入下一步 
    } catch (err) {
      message.error("指纹更新失败");
    }
  }

  /**
   * 动态修改步骤条icon
   */
  const changeIcon = (status) => {
    
    //已激活
    if(active.value > status){
      return step_success
    }
    //未激活
    if(active.value < status){
      return step_noActive
    }
    //进行中
    if(active.value == status){
      return step_active
    }
  }
</script>
<style lang="scss" scoped>
:deep(.el-step__title) {
  line-height: 24px;
  &.is-success{
    color: #222222;
  }
}
:deep{
  &.step_icon{
    width: 24px;
    height: 24px;
  }
}
.dialog{
  
  .success_tip{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 40px auto;
    justify-content: center;
    line-height: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
  
    .tip {
      line-height: 20px;
      font-size: 20px;
      letter-spacing: 0;
      margin-bottom: 12px;
    }
  }
  .wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  
    .finger {
      width: 150px;
      height: 150px;
    }
  }
}

</style>