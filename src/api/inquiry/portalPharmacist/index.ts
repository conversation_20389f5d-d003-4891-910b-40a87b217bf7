import request from '@/config/axios'
import { InquiryPrescriptionTemplateVO } from '@/api/inquiry/prescriptiontemplate'

export const InquiryPortalPharmacistApi = {
  updateClock: async (data) => {
    return await request.post({ url: '/yunnan/updatePharmacistCheckinStatus', data })
  },

  // 查询药师接诊状态
  queryPharmacistReceipt: async (data: any) => {
    return await request.put({ url: '/kernel/pharmacist/inquiry-pharmacist/get-info', data })
  },

  // 更新药师状态
  updatePharmacistReceipt: async (data: any) => {
    return await request.post({ url: '/kernel/pharmacist/inquiry-pharmacist/update-info', data })
  },

  // 获取当前药师待审核处方数量
  getAuditCount: async () => {
    return await request.get({
      url: '/kernel/pharmacist/prescription-audit/wait-receive-count',
      isLoop: true
    })
  },

  // 药师出诊
  startPharmacistReceipt: async () => {
    return await request.put({ url: '/kernel/pharmacist/inquiry-pharmacist/start-receipt' })
  },

  // 药师停诊
  stopPharmacistReceipt: async () => {
    return await request.put({ url: `/kernel/pharmacist/inquiry-pharmacist/stop-receipt` })
  },

  //获取登录用户信息
  getUserInfo: async () => {
    return await request.get({ url: `/system/user/store/get-info` })
  },

  //更新员工指纹
  updateFinger: async (data) => {
    return await request.put({ url: '/system/tenant-user-finger-print/update', data })
  },

  //创建员工指纹
  creatFinger: async (data) => {
    return await request.post({ url: '/system/tenant-user-finger-print/create', data })
  },

  //获取员工指纹
  getFinger: async () => {
    return await request.get({ url: `/system/tenant-user-finger-print/get-by-user` })
  }
}
// 审核相关
export const InquiryReviewApi = {
  // 领取一个待审核
  receivePrescriptionApi: async (params: any) => {
    return await request.get({
      url: `/kernel/pharmacist/prescription-audit/receive-prescription`,
      params
    })
  },
  // 审核通过
  auditPass: async (data: any) => {
    return await request.post({ url: `/kernel/pharmacist/prescription-audit/audit-pass`, data })
  },
  // 审核驳回
  auditReject: async (data: any) => {
    return await request.post({ url: `/kernel/pharmacist/prescription-audit/audit-reject`, data })
  },
  // 获取当前药师待审核处方数量
  waitReceiveCount: async (data: any) => {
    return await request.get({
      url: `/kernel/pharmacist/prescription-audit/wait-receive-count`,
      data
    })
  },
  // 获取CA认证状态
  getCaAuthStatusInfo: async () => {
    return await request.get({ url: `/kernel/signature/inquiry-signature-ca-auth/get` })
  },
  // 获取字典
  getDictList: async (params) => {
    return await request.get({ url: `/system/dict-data/types`, params })
  },
  // 获取处方信息
  getRecord: async (params) => {
    // return await request.get({ url: `/kernel/patient/inquiry-query/get-record`, params })
    return await request.get({ url: `/kernel/hospital/inquiry-prescription/get`, params })
  },

  // 获取处方药品信息
  getMedicine: async (params) => {
    return await request.get({ url: `/kernel/hospital/inquiry-prescription-detail/get`, params })
  },
  // 获取远程审方签名拖动图片
  getRemoteAuditSignatureUrl: async () => {
    return await request.get({
      url: `/kernel/signature/inquiry-signature-ca-auth/get-remote-audit-signature-url`,
    })
  },  // 获取远程审方签名拖动图片
  auditStartVideoCall: async (params) => {
    return await request.put({
      url: `/kernel/pharmacist/prescription-audit/audit-start-video-call`,
      params
    })
  }
}
