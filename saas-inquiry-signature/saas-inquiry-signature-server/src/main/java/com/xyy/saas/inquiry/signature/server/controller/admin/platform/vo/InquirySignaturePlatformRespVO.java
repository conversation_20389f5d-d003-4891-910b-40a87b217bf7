package com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 签章平台配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquirySignaturePlatformRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19776")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "签章平台 0-自签署 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "是否为主签章平台", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否为主签章平台")
    private Boolean master;

    @Schema(description = "属性名 eg:私有云地址 对应枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("属性名 eg:私有云地址 对应枚举")
    private String paramName;

    @Schema(description = "属性值 eg:8000809", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性值 eg:8000809")
    private String paramValue;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 平台应用扩展信息
     */
    private List<PlatformConfigExtDto> ext;

}