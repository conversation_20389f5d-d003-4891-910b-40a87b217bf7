package cn.iocoder.yudao.module.system.dal.redis.oauth2;

import static cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants.OAUTH2_ACCESS_TOKEN;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;

/**
 * @Desc @<NAME_EMAIL> @Date Created in 2023/07/14 11:21
 */
// @Repository
public class OAuth2AccessTokenMemoryDAO extends OAuth2AccessTokenRedisDAO {

    private Cache<String, OAuth2AccessTokenDO> accessTokenMap = CacheUtil.newLRUCache(10);

    @Override
    public OAuth2AccessTokenDO get(String accessToken) {
        String redisKey = formatKey(accessToken);
        return accessTokenMap.get(redisKey);
    }

    @Override
    public void set(OAuth2AccessTokenDO accessTokenDO) {
        String redisKey = formatKey(accessTokenDO.getAccessToken());
        // 清理多余字段，避免缓存
        accessTokenDO.setUpdater(null).setUpdateTime(null).setCreateTime(null).setCreator(null).setDeleted(null);
        long time = LocalDateTimeUtil.between(LocalDateTime.now(), accessTokenDO.getExpiresTime(), ChronoUnit.MILLIS);
        accessTokenMap.put(redisKey, accessTokenDO, time);
    }

    @Override
    public void delete(String accessToken) {
        String redisKey = formatKey(accessToken);
        accessTokenMap.remove(redisKey);
    }

    @Override
    public void deleteList(Collection<String> accessTokens) {
        List<String> redisKeys = CollectionUtils.convertList(accessTokens, OAuth2AccessTokenMemoryDAO::formatKey);
        if (org.springframework.util.CollectionUtils.isEmpty(redisKeys)) {
            return;
        }
        for (String key : accessTokens) {
            accessTokenMap.remove(key);
        }
    }

    private static String formatKey(String accessToken) {
        return String.format(OAUTH2_ACCESS_TOKEN, accessToken);
    }

}
