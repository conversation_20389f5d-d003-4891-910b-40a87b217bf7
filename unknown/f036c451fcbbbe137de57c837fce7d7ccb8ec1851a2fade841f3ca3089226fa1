package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "管理后台 - 门店药师关系绑定 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryTenantPharmacistRelationBindReqVO {

    @Schema(description = "药师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31948")
    @NotNull(message = "药师ID不能为空")
    private Long pharmacistId;

    @Schema(description = "门店ID")
    @NotNull(message = "门店ID不能为空")
    private List<Long> tenantIds;

    @AssertTrue(message = "门店ID不能为空")
    @JsonIgnore
    public boolean isTenantIds() {
        return tenantIds == null || CollUtil.isEmpty(tenantIds);
    }

}