package com.xyy.saas.inquiry.enums.im;

import lombok.Getter;

/**
 * IM 事件消息推送枚举
 */
@Getter
public enum ImEventPushEnum {
    PRE_INQUIRY_AUDIT("preInquiryAudit","预问诊审核事件"),

    SEND_INQUIRY("sendInquiry","医生派单"),

    DOCTOR_RECEPTION("doctorReception","医生接诊"),

    ISSUE_PRESCRIPTION("issuePrescription","医生开具处方"),

    CANCEL_PRESCRIPTION("cancelPrescription","医生取消开方"),

    ISSUE_PRESCRIPTION_TIMEOUT("issuePrescriptionTimeout","医生开方超时"),

    ISSUE_PRESCRIPTION_EVALUATE("issuePrescriptionEvaluate","医生问诊评价"),

    PRESCRIPTION_AUDIT("prescriptionAudit","处方审核");

    private final String code;
    private final String desc;

    ImEventPushEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
