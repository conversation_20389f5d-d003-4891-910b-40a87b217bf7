package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.xyy.saas.inquiry.enums.prescription.template.TemplateTypeEnum;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 处方笺模板 Response VO")
@Data
@ExcelIgnoreUnannotated
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS) // 解决dubbo序列化STRICT模式安全问题
public class InquiryPrescriptionTemplateRespVO extends BaseDto {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    private Long id;

    @Schema(description = "处方笺模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("处方笺模板名称")
    private String name;

    @Schema(description = "处方笺模板描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("处方笺模板描述")
    private String desc;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("是否禁用")
    private Boolean disable;

    /**
     * 处方笺模板类型 {@link TemplateTypeEnum}
     */
    @Schema(description = "处方笺模板类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("处方笺模板类型")
    private Integer type;

    @Schema(description = "文件 URL（底板）", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("文件 URL（底板）")
    private String url0;

    @Schema(description = "文件 URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("文件 URL")
    private String url;

    @Schema(description = "处方模板字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<PrescriptionTemplateField> templateFields;

    @Schema(description = "关联医院（使用了处方笺模板）", requiredMode = Schema.RequiredMode.REQUIRED, example = "25982")
    // @ExcelProperty("关联医院")
    private List<String> usedRelatedHospitalNameList;

}