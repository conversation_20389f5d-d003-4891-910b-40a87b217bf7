package com.xyy.saas.inquiry.drugstore.server.api.user;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.drugstore.api.user.UserApi;
import com.xyy.saas.inquiry.drugstore.server.service.user.UserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @DateTime: 2025/5/8 15:40
 * @Description: USER API实现类
 **/
@Service
public class UserApiImpl implements UserApi {

    @Resource
    private UserService userService;

    /**
     * @return
     */
    @Override
    public AdminUserRespDTO getUser() {
        return userService.getUser();
    }
}
