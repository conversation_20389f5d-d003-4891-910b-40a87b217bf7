package com.xyy.saas.inquiry.enums.prescription.template;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 模板类型
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午8:27
 */
@Getter
@RequiredArgsConstructor
public enum TemplateTypeEnum implements IntArrayValuable {

    ASIAN_MEDICINE(0, "西药"),

    CHINESE_MEDICINE(1, "中药"),
    ;

    private final int code;

    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TemplateTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static TemplateTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid MedicineTypeEnum code: " + code));
    }

}
