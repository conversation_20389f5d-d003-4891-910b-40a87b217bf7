package com.xyy.saas.inquiry.drugstore.api.tenant.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:56
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TenantPackageCostDto implements Serializable {

    /**
     * 门店id
     */
    @NotNull(message = "门店id不能为空")
    private Long tenantId;

    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;

    /**
     * 门店套餐id
     */
    @NotNull(message = "门店套餐id关系不能为空")
    private Long tenantPackageId;

    /**
     * 问诊医院id
     */
    private List<String> hospitalPrefs;


    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;

    /**
     * 问诊审核类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum}
     */
    private Integer inquiryAuditType;

    /**
     * 旧套餐包门店套餐关联id
     */
    private Long oldTenantPackageId;

    /**
     * 问诊套餐items
     */
    private List<InquiryPackageItem> inquiryPackageItems;

    /**
     * 问诊类型 {@link InquiryWayTypeEnum}
     */
    private Integer inquiryWayType;
    /**
     * 问诊额度 -1不限
     */
    private Long cost;
    /**
     * 问诊剩余额度 -1不限
     */
    private Long surplusCost;

    /**
     * 套餐开始时间
     */
    @NotNull(message = "问诊套餐开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 套餐结束时间
     */
    @NotNull(message = "问诊套餐结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 套餐订单状态 {@link TenantPackageRelationStatusEnum}
     */
    private Integer status;

    /**
     * 是否状态变更
     */
    private boolean updateStatus;

    /**
     * 创建人
     */
    private String creator;


}
