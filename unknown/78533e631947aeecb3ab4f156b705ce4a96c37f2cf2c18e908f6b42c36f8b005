package com.xyy.saas.inquiry.hospital.server.mq.producer.doctor;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.DoctorDistributeEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/26 10:03
 * @Description: 医生调度事件生产者
 */
@Component
@EventBusProducer(
    topic = DoctorDistributeEvent.TOPIC
)
public class DoctorDistributeProducer extends EventBusRocketMQTemplate {

}
