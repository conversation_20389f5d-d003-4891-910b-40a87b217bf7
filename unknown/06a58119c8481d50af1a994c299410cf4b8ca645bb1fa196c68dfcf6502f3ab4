package com.xyy.saas.inquiry.im.server.controller.app.callback;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentCallBackRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;
import com.xyy.saas.inquiry.im.server.service.trtc.InquiryTrtcService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @Date: 2024/12/06 10:46
 * @Description: 腾讯云TRTC回调接口控制器
 */
@Tag(name = "app - 腾讯trtc回调接口")
@RestController
@RequestMapping("/trtc/callback")
@Validated
public class TencentTrtcCallBackController {

    @Resource
    private InquiryTrtcService inquiryTrtcService;

    private static final Logger log = LoggerFactory.getLogger(TencentTrtcCallBackController.class);

    @PostMapping("/message")
    @Operation(summary = "trtc 房间与媒体回调")
    @PermitAll
    public TencentCallBackRespVO onMessage(@RequestBody Map<String,Object> params) {
        log.info("腾讯trtc房间与媒体回调,入参：{}", JSON.toJSONString(params));
        TencentTrtcCallBackReqVO reqVO = JSON.parseObject(JSON.toJSONString(params), TencentTrtcCallBackReqVO.class);
        inquiryTrtcService.tencentTrtcCallBack(reqVO);
        return TencentCallBackRespVO.callBackSuccess();
    }
}
