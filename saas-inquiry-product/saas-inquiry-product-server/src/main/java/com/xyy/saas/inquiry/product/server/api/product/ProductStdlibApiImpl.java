package com.xyy.saas.inquiry.product.server.api.product;


import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
public class ProductStdlibApiImpl implements ProductStdlibApi {

    @Resource
    private ProductStdlibService productStdlibService;

    /**
     * 查询自建标准库商品
     *
     * @param searchDto 查询条件
     * @return 去重后的商品通用名列表
     */
    @Override
    public List<ProductStdlibDto> searchProductStdlibList(StdlibProductSearchDto searchDto, int limit) {
        return productStdlibService.searchStdlibProductList(searchDto, limit);
    }
}
