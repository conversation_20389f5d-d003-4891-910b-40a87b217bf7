package com.xyy.saas.inquiry.product.server.service.product.mid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class MeProductFiledDescUtil {


    public Map<String ,List<Map>> dictMap = new HashMap<>();

    //处方药 单轨/双轨
    public static Map<Byte, String> prescriptionYnMap = new HashMap<>();
    //商品大类
    public static Map<Integer, String> spuCategoryMap = new HashMap<>();
    //存储属性(1:外用、2:内服、3:注射剂、4:食品、5:保健食品、9：其他)
    public static Map<Byte, String> shadingAttrMap = new HashMap<>();
    public static  Map<Integer, String> shadingAttrIntMap = null;//历史原因，有用Integer，有用Byte
    /** 存储属性 反向map*/
    public static Map<String, Byte> shadingAttrRevMap = null;
    public static Map<String, Integer> shadingAttrIntRevMap = null;//历史原因，有用Integer，有用Byte

    //是否监管
    public static Map<Byte, String> whetherSupervision = new HashMap<>();

    // 管理属性
    public static Map<Integer, String> manageAttrMap = new HashMap<>();
    // 备货属性
    public static Map<Integer, String> logisticsAttrMap = new HashMap<>();
    // 是否控销品
    public static Map<Integer, String> controlSalesYnMap = new HashMap<>();
    // 是否退换货
    public static Map<Integer, String> replacementYnMap = new HashMap<>();
    // 经营属性
    public static Map<Integer, String> managementAttrMap = new HashMap<>();
    //审批zhuangt
    public static Map<Byte, String> approvalStatusMap = new HashMap<>();


    //商品大类中台与saas映射
    public static Map<String, Integer> spuForPlatformMAp = new HashMap<>();
    static {
        prescriptionYnMap.put((byte)0,"双轨处方药");
        prescriptionYnMap.put((byte)1,"单轨处方药");

        spuCategoryMap.put(1,"普通药品");
        spuCategoryMap.put(2,"中药");
        spuCategoryMap.put(3, "医疗器械");
        spuCategoryMap.put(4, "其他");
        spuCategoryMap.put(5,"非药");
        spuCategoryMap.put(6,"赠品");

        shadingAttrMap.put((byte)1, "外用");
        shadingAttrMap.put((byte)2, "内服");
        shadingAttrMap.put((byte)3, "注射剂");
        shadingAttrMap.put((byte)4, "食品");
        shadingAttrMap.put((byte)5, "保健食品");
        shadingAttrMap.put((byte)9, "其他");
        shadingAttrIntMap = shadingAttrMap.entrySet().stream().collect(Collectors.toMap(p->p.getKey().intValue(), Entry::getValue));
        shadingAttrRevMap = shadingAttrMap.entrySet().stream().collect(Collectors.toMap(Entry::getValue, Entry::getKey));
        shadingAttrIntRevMap = shadingAttrMap.entrySet().stream().collect(Collectors.toMap(Entry::getValue, p->p.getKey().intValue()));

        whetherSupervision.put((byte)1, "是");
        whetherSupervision.put((byte)0, "否");

        manageAttrMap.put(1, "自有品牌");
        manageAttrMap.put(2, "战略品种");
        manageAttrMap.put(3, "统采毛利");
        manageAttrMap.put(4, "统采品牌");
        manageAttrMap.put(5, "地采补充");
        manageAttrMap.put(6, "地采品牌");
        manageAttrMap.put(7, "地采毛利");

        logisticsAttrMap.put(2, "淘汰");
        logisticsAttrMap.put(1, "必备");
        logisticsAttrMap.put(0, "选配");

        controlSalesYnMap.put(1, "是");
        controlSalesYnMap.put(0, "否");

        replacementYnMap.put(1, "是");
        replacementYnMap.put(0, "否");

        managementAttrMap.put(1, "S");
        managementAttrMap.put(2, "A");
        managementAttrMap.put(3, "B");
        managementAttrMap.put(4, "C");
        managementAttrMap.put(5, "D");

        approvalStatusMap.put((byte)1, "审批中");
        approvalStatusMap.put((byte)2, "已通过");
        approvalStatusMap.put((byte)3, "已驳回");

        spuForPlatformMAp.put("1", 1);
        spuForPlatformMAp.put("2", 2);
        spuForPlatformMAp.put("3", 3);
        spuForPlatformMAp.put("4", 5);
        spuForPlatformMAp.put("5", 6);
    }

    MeProductFiledDescUtil(){
        super();
        // init();
    }

    // public void init(){
    //     //初始化map
    //     Config config = ConfigService.getAppConfig();
    //     Set<String> set =  config.getPropertyNames();
    //     setDictMap(config, set);
    //     //动态监听配置变化
    //     config.addChangeListener(new ConfigChangeListener() {
    //         @Override
    //         public void onChange(ConfigChangeEvent changeEvent) {
    //             Set<String> keys = changeEvent.changedKeys();
    //             setDictMap(config, keys);
    //         }
    //     });
    // }
    //
    // public void setDictMap(Config config, Set<String> set){
    //     for(String key: set){
    //         if (key.startsWith(Constant.A_DICT)){
    //             String value = config.getProperty(key, "");
    //             if (StringUtil.isNotEmpty(value)){
    //                 List<Map> list = JSONUtils.parse2List(value, Map.class);
    //                 dictMap.put(key, list);
    //             }
    //         }
    //     }
    // }

    public List<Map> getAllDictForType(String type){
        return dictMap.get(type);
    }

    public Map getOneExampleForType(String type, Object value){
        List<Map> maps =  dictMap.get(type);
        if (maps==null){
            return null;
        }
        for (Map map: maps){
            if (map.get("value").toString().equals(value.toString())){
                return map;
            }
        }
        return null;
    }
}
