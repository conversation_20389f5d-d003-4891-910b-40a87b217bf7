package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;

import java.util.Arrays;

public enum AuditStatusEnum implements IntArrayValuable {
    // 0, 待审核
    PENDING(0, "待审核"),

    // 1, 审核通过
    APPROVED(1, "审核通过"),

    // 2, 审核驳回
    REJECTED(2, "审核驳回");

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(AuditStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 枚举类的构造函数，必须是私有的
    AuditStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据整数值获取枚举实例的静态方法
    public static AuditStatusEnum fromCode(int code) {
        for (AuditStatusEnum status : AuditStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
