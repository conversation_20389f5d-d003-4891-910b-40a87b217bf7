package com.xyy.saas.inquiry.mq.inquiry;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryCreateMessage;
import lombok.Builder;
import lombok.Data;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/12 下午8:13
 */
@Builder
@Data
public class InquiryRecordCreateEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "INQUIRY_RECORD_CREATE";

    private InquiryCreateMessage msg;


    @JsonCreator
    public InquiryRecordCreateEvent(@JsonProperty("msg") InquiryCreateMessage msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
