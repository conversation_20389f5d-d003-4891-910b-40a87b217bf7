package com.xyy.saas.inquiry.signature.server.controller.admin.signature.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 签章平台用户电子签名图片 saveVO")
@Data
@Accessors(chain = true)
public class InquiryUserElectronicSignatureSaveVO {

    @Schema(description = "userId", requiredMode = Schema.RequiredMode.REQUIRED, example = "19776")
    @NotNull(message = "userId不能为空")
    private Long userId;

    @Schema(description = "签章平台 0-自签署 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer signaturePlatform;


}