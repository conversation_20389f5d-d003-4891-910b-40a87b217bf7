<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
    <el-form-item label="流程名称" prop="headTenantId">
        <el-input
          v-model="queryParams.headTenantId"
          placeholder="请输入流程名称"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" class="bc-00b955"
          >搜索</el-button
        >
        <el-button @click="resetQuery" class="resetBtn"> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
    <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="流程名称" align="center" prop="pref" />
      <el-table-column label="业务编码" align="center" prop="pref" />
      <el-table-column label="发起人" align="center" prop="pref" />
      <el-table-column label="发起时间" align="center" prop="currentHandler" />
      <el-table-column label="审批时间" align="center" prop="currentHandler" />
      <el-table-column label="耗时" align="center" prop="currentHandler" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row.id)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <!-- <ProductQualityChangeRecordForm ref="formRef" @success="getList" /> -->

</template>

<script setup lang="ts">
import download from '@/utils/download'
import { ProductQualityChangeRecordApi, ProductQualityChangeRecordVO } from '@/api/inquiry/product/gsp/qualityChange'

defineOptions({ name: 'ProductQualityChangeRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ProductQualityChangeRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  type: undefined,
  pref: undefined,
  applicant: undefined,
  createTime: [],
  approvalStatus: undefined,
  currentHandler: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProductQualityChangeRecordApi.getProductQualityChangeRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProductQualityChangeRecordApi.deleteProductQualityChangeRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProductQualityChangeRecordApi.exportProductQualityChangeRecord(queryParams)
    download.excel(data, '商品变更记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref({}) // 选中行
const handleCurrentChange = (row) => {
  currentRow.value = row
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.bc-00b955 {
  padding: 11px 16px;
  border-radius: 4px;
  background-color: var(--el-color-primary);
  color: #ffffff;
}
.table-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}
</style>