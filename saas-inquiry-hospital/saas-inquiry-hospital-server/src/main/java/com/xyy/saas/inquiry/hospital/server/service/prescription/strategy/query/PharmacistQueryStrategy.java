package com.xyy.saas.inquiry.hospital.server.service.prescription.strategy.query;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import jakarta.annotation.Resource;
import java.util.Objects;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/10/31 9:53
 * @Description: 药师查询处方参数设置策略
 */
@Component
public class PharmacistQueryStrategy implements QueryStrategy {

    @Resource
    private InquiryPharmacistApi inquiryPharmacistApi;

    @Resource
    private PermissionApi permissionApi;

    /**
     * 设置相关参数
     *
     * @param pageReqVO 查询参数
     */
    @Override
    public void setParam(InquiryPrescriptionPageReqVO pageReqVO) {
        // 根据userid 查询药师信息
        boolean isPharmacist = permissionApi.hasAnyRoles(Objects.requireNonNull(getLoginUser()).getId(), RoleCodeEnum.PHARMACIST.getCode());
        if (isPharmacist) {
            InquiryPharmacistDto pharmacistDto = inquiryPharmacistApi.getPharmacistByUserId(Objects.requireNonNull(getLoginUser()).getId());
            // 设置药师guid
            pageReqVO.setPharmacistPref(pharmacistDto.getPref());
        }

        if (pageReqVO.getTenantId() != null) {
            // 如果入参门店有id,将门店id转换到目前门店id
            pageReqVO.setTargetTenantId(pageReqVO.getTenantId());
        }

        // 查询当前门店的处方
        pageReqVO.setTenantId(Objects.requireNonNull(getLoginUser()).getTenantId());
        // 药师需要根据审核时间查询
        pageReqVO.setAuditPrescriptionTime(pageReqVO.getOutPrescriptionTime());
        pageReqVO.setOutPrescriptionTime(null);
    }

    /**
     * 获取当前策略对应的查询场景
     *
     * @return 查询场景
     */
    @Override
    public QuerySceneEnum getQueryScene() {
        return QuerySceneEnum.PHARMACIST;
    }
}
