import request from '@/config/axios'

export interface UserVO {
  id: number
  username: string
  nickname: string
  deptId: number
  postIds: string[]
  email: string
  mobile: string
  idCard: string
  sex: number
  avatar: string
  loginIp: string
  status: number
  accountStatus: number
  onlyDefaultTenant: boolean
  remark: string
  loginDate: Date
  createTime: Date,
  tenantUserRelationId:number,
  tenantAdminUserId:number,
  tenantId:number,
  roleCodes:string[],
  needClockIn:number,
}

// 查询员工管理列表
export const getUserPageStore = (params: PageParam) => {
  return request.get({ url: '/system/user/store/page', params })
}


// 查询用户详情
export const getUser = (id: number) => {
  return request.get({ url: '/system/user/store/get?id=' + id })
}

// 新增用户
export const createUser = (data: UserVO) => {
  return request.post({ url: '/system/user/store/create', data })
}

// 修改用户
export const updateUser = (data: UserVO) => {
  return request.put({ url: '/system/user/store/update', data })
}

// 删除用户
export const deleteUserStore = (id: number, tid?: number) => {
  if (tid) {
    return request.delete({ url: '/system/user/store/delete?id=' + id + '&tenantId=' + tid })
  }
  return request.delete({ url: '/system/user/store/delete?id=' + id })
}

export const deleteUserSystem = (id: number) => {
  return request.delete({ url: '/system/user/system/delete?id=' + id })
}


// 导出用户
export const exportUser = (params) => {
  return request.download({ url: '/system/user/export', params })
}

// 下载用户导入模板
export const importUserTemplate = () => {
  return request.download({ url: '/system/user/get-import-template' })
}

// 用户密码重置
export const resetUserPwd = (id: number, password: string) => {
  const data = {
    id,
    password
  }
  return request.put({ url: '/system/user/store/update-password', data: data })
}

// 用户状态修改
export const updateEmployeeStatus = (id: number, status: number, tenantId?: number) => {
  const data = { id, status, tenantId }
  return request.put({ url: '/system/user/store/update-status', data: data })
}

// 用户上下班是否需要打开状态修改
export const updateNeedClockIn = (data: any) => {
  return request.post({ url: '/system/tenant-user-relation/update-need-clock-in', data })
}

// 获取用户精简信息列表
export const getSimpleUserList = (): Promise<UserVO[]> => {
  return request.get({ url: '/system/user/store/simple-list' })
}



// 查询用户管理列表
export const getUserPageSystem = (params: PageParam) => {
  return request.get({ url: '/system/user/system/page', params })
}

// 用户状态修改
export const updateUserStatus = (id: number, status: number) => {
  const data = { id, status }
  return request.put({ url: '/system/user/system/update-status', data: data })
}

// 查询用户-by 手机号
export const getUserByMobileStore = (mobile: string) => {
  return request.get({ url: '/system/user/store/get-by-mobile?mobile=' + mobile })
}

// 查询用户-by 手机号
export const getUserByMobileSystem = (params: any) => {
  return request.get({ url: '/system/user/system/create-get-by-mobile', params })
}

//转让门店
export const transferStore = (tenantId: string, userId: string, mobile: string, code: number) => {
  return request.put({
    url: '/system/tenant/transfers',
    data: {
      tenantId: tenantId, //门店id
      userId: userId,//选择员工id
      mobile: mobile,//
      code: code
    },
    ignoreError: true
  })
}

//获取用户精简列表
export const CondenseUsersList = (queryReqVO) => {
  return request.get({
    url: '/system/user/store/simple-list',
    params: queryReqVO
  });
};
