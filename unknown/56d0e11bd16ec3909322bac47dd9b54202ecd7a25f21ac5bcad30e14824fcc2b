package cn.iocoder.yudao.module.system.dal.mysql.appversion;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;


/**
 * App版本 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AppVersionMapper extends BaseMapperX<AppVersionDO> {

    default PageResult<AppVersionDO> selectPage(AppVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AppVersionDO>()
            .eqIfPresent(AppVersionDO::getAppBiz, reqVO.getAppBiz())
            .likeIfPresent(AppVersionDO::getAppVersion, reqVO.getAppVersion())
            .eqIfPresent(AppVersionDO::getAppVersionCode, reqVO.getAppVersionCode())
            .eqIfPresent(AppVersionDO::getAppVersionDesc, reqVO.getAppVersionDesc())
            .eqIfPresent(AppVersionDO::getOsType, reqVO.getOsType())
            .eqIfPresent(AppVersionDO::getMinOsType, reqVO.getMinOsType())
            .eqIfPresent(AppVersionDO::getDownloadUrl, reqVO.getDownloadUrl())
            .eqIfPresent(AppVersionDO::getUpgradeType, reqVO.getUpgradeType())
            .eqIfPresent(AppVersionDO::getUpgradeScope, reqVO.getUpgradeScope())
            .eqIfPresent(AppVersionDO::getGrayRatio, reqVO.getGrayRatio())
            .eqIfPresent(AppVersionDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(AppVersionDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(AppVersionDO::getAppVersionCode));
    }


    default List<AppVersionDO> selectList(AppVersionPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AppVersionDO>()
            .eqIfPresent(AppVersionDO::getAppBiz, reqVO.getAppBiz())
            .eqIfPresent(AppVersionDO::getAppVersion, reqVO.getAppVersion())
            .eqIfPresent(AppVersionDO::getAppVersionCode, reqVO.getAppVersionCode())
            .eqIfPresent(AppVersionDO::getAppVersionDesc, reqVO.getAppVersionDesc())
            .eqIfPresent(AppVersionDO::getOsType, reqVO.getOsType())
            .eqIfPresent(AppVersionDO::getMinOsType, reqVO.getMinOsType())
            .eqIfPresent(AppVersionDO::getDownloadUrl, reqVO.getDownloadUrl())
            .eqIfPresent(AppVersionDO::getUpgradeType, reqVO.getUpgradeType())
            .eqIfPresent(AppVersionDO::getUpgradeScope, reqVO.getUpgradeScope())
            .eqIfPresent(AppVersionDO::getGrayRatio, reqVO.getGrayRatio())
            .eqIfPresent(AppVersionDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(AppVersionDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(AppVersionDO::getAppVersionCode));
    }
}