package com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;

/**
 * @ClassName：CreateImUserDO
 * @Author: xucao
 * @Date: 2024/11/27 18:49
 * @Description: 腾讯创建IM用户的数据模型
 */
@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TencentImportImUserDO implements Serializable {
    @JSONField(name = "UserID")
    private String userID;

    @JSONField(name = "Nick")
    private String nick;

    @JSONField(name = "FaceUrl")
    private String faceUrl;
}
