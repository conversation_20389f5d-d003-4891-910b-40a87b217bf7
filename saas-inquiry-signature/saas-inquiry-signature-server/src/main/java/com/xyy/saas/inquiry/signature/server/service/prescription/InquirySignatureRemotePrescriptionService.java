package com.xyy.saas.inquiry.signature.server.service.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import jakarta.validation.Valid;

/**
 * 问诊远程处方签章service
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:47
 */
public interface InquirySignatureRemotePrescriptionService {


    /**
     * 审核远程处方笺
     *
     * @param psAuditDto 审核dto
     */
    CommonResult<?> auditRemotePrescription(@Valid PrescriptionSignatureAuditDto psAuditDto);

}
