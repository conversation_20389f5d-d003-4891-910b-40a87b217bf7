package com.xyy.saas.inquiry.config.dubbo.filter;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.util.monitor.TraceIdUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.dubbo.common.Constants;
import org.apache.dubbo.common.constants.CommonConstants;
import com.xyy.saas.inquiry.constant.TenantConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.RpcServiceContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Profile;

/**
 * Dubbo过滤器 利用SPI机制注册过滤器 拦截RMI 设置TraceId
 * <p>
 * /resources/META-INF/dubbo/org.apache.dubbo.rpc.Filter 在yml 的dubbo配置中生效，
 * 例如:
 * dubbo:
 *   consumer:
 *     filter: dubboTenantFilter
 *   provider:
 *     filter: dubboTenantFilter
 *
 * @Author:chenxiaoyi
 */
@Slf4j
// @Profile("!local")
@ConditionalOnClass(name = "org.apache.dubbo.rpc.Filter")
@Activate(group = {CommonConstants.PROVIDER, CommonConstants.CONSUMER})
public class DubboTenantFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        RpcServiceContext rpcContext = RpcContext.getServiceContext();
        // 判断如果当前provider和consumer的applicationName相同,则不清除MDC
        if (rpcContext.getUrl() == null || StringUtils.equals(rpcContext.getUrl().getApplication(), rpcContext.getRemoteApplicationName())) {
            return invoker.invoke(invocation);
        }

        // before
        if (rpcContext.isConsumerSide()) {
            // log.info("请求的机器地址ip是:{}", rpcContext.getUrl().getHost());
            invocation.setAttachment(TraceIdUtil.TRACE_ID, TraceIdUtil.getDefaultIfBlank());
            // 如果是消费者 获取门店信息传递下去
            invocation.setAttachment(TenantConstant.TENANT_ID, TenantContextHolder.getTenantId() == null ? null : TenantContextHolder.getTenantId().toString());
            invocation.setAttachment(TenantConstant.TENANT_IGNORE, Boolean.toString(TenantContextHolder.isIgnore()));
            invocation.setAttachment(TenantConstant.THIRD_APP_ID, TenantContextHolder.getThirdAppId() == null ? null : TenantContextHolder.getThirdAppId().toString());
        }
        if (rpcContext.isProviderSide()) {
            // traceId
            TraceIdUtil.putDefaultIfBlank(invocation.getAttachment(TraceIdUtil.TRACE_ID));
            // 如果是生产者 从 rpcContext获取 门店信息
            String attachmentTenantId = invocation.getAttachment(TenantConstant.TENANT_ID);
            if (StringUtils.isNotBlank(attachmentTenantId)) {
                TenantContextHolder.setTenantId(NumberUtil.parseLong(attachmentTenantId));
            }
            String attachmentTenantIgnore = invocation.getAttachment(TenantConstant.TENANT_IGNORE);
            if (StringUtils.isNotBlank(attachmentTenantIgnore)) {
                TenantContextHolder.setIgnore(Boolean.valueOf(attachmentTenantIgnore));
            }
            String thirdAppId = invocation.getAttachment(TenantConstant.THIRD_APP_ID);
            if (StringUtils.isNotBlank(thirdAppId)) {
                TenantContextHolder.setThirdAppId(NumberUtil.parseLong(thirdAppId));
            }
        }
        // invoke
        Result result = invoker.invoke(invocation);
        // after 提供者 也就是被调用方 在处理完请求后，将自己的MDC清除掉
        if (rpcContext.isProviderSide()) {
            TraceIdUtil.clear();
        }
        return result;
    }
}