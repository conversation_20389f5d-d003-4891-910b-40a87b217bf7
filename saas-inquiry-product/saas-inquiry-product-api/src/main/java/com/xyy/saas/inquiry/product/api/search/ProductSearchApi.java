package com.xyy.saas.inquiry.product.api.search;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryProductSearchReqDto;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商品搜索服务
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/20 15:49
 */
public interface ProductSearchApi {

    /**
     * 根据标准库id搜索商品
     *
     * @param standardIds 标准库id
     * @return 商品列表
     */
    CommonResult<List<InquiryProductDetailDto>> queryProductStandardListByStandardIds(List<String> standardIds);

    /**
     * 搜索商品关联诊断信息
     *
     * @param reqDto
     * @return
     */
    CommonResult<List<InquiryDiagnosticsSearchRespDto>> productDiagnostics(InquiryDiagnosticsSearchReqDto reqDto);

    /**
     * 转发 根据69码查询商品用法用量规则
     *
     * @return ForwardResult
     */
    CommonResult<List<InquiryProductDetailDto>> getProductStandardListByBarcodeList(@RequestBody List<String> barCodeList, Integer medicineType);

    /**
     * 转发 根据批准文号查询商品用法用量规则
     *
     * @return ForwardResult
     */
    CommonResult<List<InquiryProductDetailDto>> getProductStandardListByApprovalNoList(@RequestBody List<String> approvalNoList, Integer medicineType);

    /**
     * 转发 根据商品名称和类型批量查询商品
     *
     * @return ForwardResult
     */
    CommonResult<List<InquiryProductDetailDto>> getProductStandardByProductNamesAndType(@RequestBody List<String> commonNameList, Integer medicineType);

}
