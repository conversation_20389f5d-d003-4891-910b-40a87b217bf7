package com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

@Schema(description = "管理后台 - 药师信息 Response VO")
@Data
public class InquiryPharmacistForwardRespVO implements Serializable {

    /**
     * 编码
     */
    private Long id;

    /**
     * 药师guid
     */
    private String guid;

    /**
     * 药师姓名
     */
    private String name;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 法大大实名认证状态 0: 待认证; 1: 认证完成
     */
    private Integer fddCertifyStatus;

    /**
     * 免验证签类型 1 未完成 2 已完成 3 授权临期
     */
    private Integer visaFreeStatus;

    /**
     * 审核状态：0 待审核 1审核通过 2 审核不通过
     */
    private Byte examineStatus;

    /**
     * 药师类型 - 执业资格，0平台药师 1药店药师
     */
    private Byte pharmacistType;

    /**
     * 绑定门店名称
     */
    private String drugstoreName;

    /**
     * 合作状态：0 取消合作 1 合作中
     */
    private Byte cooperationStatus;

    /**
     * 出诊状态：0 未出诊 1 出诊中
     */
    private Byte inquiryStatus;


    private String createTime;
}